package com.howbuy.tms.high.batch.service.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.mapper.customize.order.HighDealOrderDtlPoMapper;
import com.howbuy.tms.high.batch.dao.po.batch.SimuFundCheckOrderDto;
import com.howbuy.tms.high.batch.dao.po.order.HighDealDtlAndCheckOrderPo;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPoExample;
import com.howbuy.tms.high.batch.dao.po.order.SpecialProductOrderPo;
import com.howbuy.tms.high.batch.dao.vo.*;
import com.howbuy.tms.high.batch.facade.query.querydealdtlinterpose.bean.QueryHighDealDtlInterposeCondition;
import com.howbuy.tms.high.batch.facade.query.queryfunddealorderdtl.bean.QueryHighFundDealOrderDtlCondition;
import com.howbuy.tms.high.batch.facade.query.queryownershiprighttransfer.bean.QueryOwnershipRightTransferParam;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.tms.orders.facade.enums.FundDivModeEnum;
import com.howbuy.tms.orders.facade.enums.NotifySubmitFlagEnum;
import com.howbuy.tms.orders.facade.enums.TxAckFlagEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class HighDealOrderDtlRepository {
    @Autowired
    private HighDealOrderDtlPoMapper highDealOrderDtlPoMapper;

    public HighDealOrderDtlPo selectByDealDtlNo(String dealDtlNo) {
        HighDealOrderDtlPoExample example = new HighDealOrderDtlPoExample();
        example.createCriteria().andDealDtlNoEqualTo(dealDtlNo);
        List<HighDealOrderDtlPo> dealOrderDtlPoList = highDealOrderDtlPoMapper.selectByExample(example);
        return dealOrderDtlPoList.isEmpty() ? null : dealOrderDtlPoList.get(0);
    }

    public List<String> selectByDealNoListAndTaCodeList(List<String> taCodes, List<String> dealNoList) {
        return highDealOrderDtlPoMapper.selectByDealNoListAndTaCodeList(taCodes, dealNoList);
    }

    public List<HighDealOrderDtlPo> selectNotDualentryAndCallback(String submitTaDt, List<String> taCodes) {
        return highDealOrderDtlPoMapper.selectNotDualentryAndCallback(submitTaDt, taCodes);
    }

    public List<HighDealOrderDtlPo> selectNotPassCalm(String submitTaDt, List<String> taCodes, Date date) {
        return highDealOrderDtlPoMapper.selectNotPassCalm(submitTaDt, taCodes, date);
    }

    public List<HighDealOrderDtlPo> selectInvalidAssetcertificate(String submitTaDt, List<String> taCodes) {
        return highDealOrderDtlPoMapper.selectInvalidAssetcertificate(submitTaDt, taCodes);
    }


    public List<HighDealOrderDtlPo> selectNotNeedNotify(String submitTaDt, List<String> taCodes, Date date) {
        return highDealOrderDtlPoMapper.selectNotNeedNotify(submitTaDt, taCodes, date);
    }

    /**
     * getByDealNo:通过订单号查询高端订单明细信息
     *
     * @param dealNo 订单号
     * @return List<HighDealOrderDtlPo>
     * <AUTHOR>
     * @date 2017年3月30日 下午5:20:01
     */
    public List<HighDealOrderDtlPo> getByDealNo(String dealNo) {
        HighDealOrderDtlPoExample example = new HighDealOrderDtlPoExample();
        example.createCriteria().andDealNoEqualTo(dealNo);
        return highDealOrderDtlPoMapper.selectByExample(example);
    }

    /**
     * getByDealNoList:查询多个订单明细
     *
     * @param dealNos
     * @return
     * <AUTHOR>
     * @date 2019年2月14日 下午3:36:16
     */
    public List<HighDealOrderDtlPo> getByDealNoList(List<String> dealNos) {
        return highDealOrderDtlPoMapper.selectByDealNoList(dealNos);
    }


    public void insertSelective(HighDealOrderDtlPo highDealOrderDtlPo) {
        highDealOrderDtlPoMapper.insertSelective(highDealOrderDtlPo);
    }

    /**
     * 根据上报日查询订单
     */
    public List<HighDealOrderDtlPo> queryHighDealOrderListBySubmitTaDt(String taTradeDt, List<String> mBusiCodeList) {
        HighDealOrderDtlPoExample example = new HighDealOrderDtlPoExample();
        example.createCriteria().andTaTradeDtEqualTo(taTradeDt).andMBusiCodeIn(mBusiCodeList);
        return highDealOrderDtlPoMapper.selectByExample(example);
    }


    /**
     * getDealOrdersToNotify:(查询待通知/需要重新通知的订单明细信息)
     *
     * @param tradeDt
     * @return
     * <AUTHOR>
     * @date 2018年6月20日 下午4:22:29
     */
    public List<SimuFundCheckOrderDto> getDealOrdersToNotify(String tradeDt) {
        return highDealOrderDtlPoMapper.selectDealOrdersToNotify(tradeDt);
    }

    /**
     * 查询待上报订单信息
     *
     * @return 订单信息
     */
    public List<WaitSubmitOrderDto> queryWaitSubmitDealOrderDtl(Date endDate) {
        return highDealOrderDtlPoMapper.queryWaitSubmitDealOrderDtl(endDate);
    }

    /**
     * 查询未通知/需重新通知的子订单明细列表
     *
     * @param dealNo
     * @return java.util.List<com.howbuy.tms.high.batch.dao.po.batch.SimuFundCheckOrderDto>
     * @author: huaqiang.liu
     * @date: 2021/3/11 15:55
     * @since JDK 1.8
     */
    public List<SimuFundCheckOrderDto> getMergeSubmitOrdersToNotify(String dealNo) {
        return highDealOrderDtlPoMapper.selectMergeSubmitOrdersToNotify(dealNo);
    }

    /**
     * updateByDealDtlNo:根据订单号更新订单明细信息
     *
     * @return
     * <AUTHOR>
     * @date 2017年7月7日 下午2:42:27
     */
    public int updateByDealDtlNo(HighDealOrderDtlPo highDealOrderDtlPo) {
        HighDealOrderDtlPoExample example = new HighDealOrderDtlPoExample();
        example.createCriteria().andDealDtlNoEqualTo(highDealOrderDtlPo.getDealDtlNo());
        return highDealOrderDtlPoMapper.updateByExampleSelective(highDealOrderDtlPo, example);
    }

    /**
     * updateAppFlagAndTaTradeDt:更新交易申请状态和ta工作日
     *
     * @param dealDtlNo      订单明细号
     * @param txAppFlag      交易申请状态
     * @param taTradeDt      TA工作日
     * @param oldTxAppFlag   原交易申请状态
     * @param cancelOrderSrc 撤销来源
     * @param now            时间
     * @return
     * <AUTHOR>
     * @date 2017年7月12日 上午11:27:12
     */
    public int updateAppFlagAndTaTradeDt(String dealDtlNo, String txAppFlag, String taTradeDt, String oldTxAppFlag, String cancelOrderSrc, Date now) {
        return highDealOrderDtlPoMapper.updateAppFlagAndTaTradeDt(dealDtlNo, txAppFlag, taTradeDt, oldTxAppFlag, cancelOrderSrc, now);
    }

    /**
     * updateAppFlagAndSubmitFlag:更新交易订单明细中的交易申请状态或通知上报状态
     *
     * @param dealDtlNo        交易订单明细
     * @param txAppFlag        交易申请标记
     * @param notifySubmitFlag 通知上报标记
     * @param oldUpdateDtm     上次最后更新时间戳
     * @param cancelOrderSrc   撤单来源
     * @param memo             备注
     * @param now              当前时间
     * @return
     * <AUTHOR>
     * @date 2017年7月12日 上午11:28:25
     */
    public int updateAppFlagAndSubmitFlag(String dealDtlNo, String txAppFlag, String notifySubmitFlag, String cancelOrderSrc, String memo, Date oldUpdateDtm,
                                          Date now) {
        return highDealOrderDtlPoMapper.updateAppFlagOrSubmitFlag(dealDtlNo, txAppFlag, notifySubmitFlag, cancelOrderSrc, memo, oldUpdateDtm, now);
    }

    /**
     * updateAppFlagAndSubmitFlag:更新交易订单明细中的交易申请状态或通知上报状态
     *
     * @param dealDtlNo
     * @param txAppFlag
     * @param notifySubmitFlag
     * @param oldUpdateDtm
     * @return
     * <AUTHOR>
     * @date 2017年7月7日 下午1:39:03
     */
    public int updateAppFlagAndSubmitFlag(String dealDtlNo, String txAppFlag, String notifySubmitFlag, Date oldUpdateDtm) {
        return highDealOrderDtlPoMapper.updateAppFlagOrSubmitFlag(dealDtlNo, txAppFlag, notifySubmitFlag, null, null, oldUpdateDtm, new Date());
    }

    /**
     * 更新交易订单明细中的通知上报状态
     *
     * @param dealDtlNo
     * @param notifySubmitFlag
     * @param oldUpdateDtm
     * @param mainDealOrderNo
     * @return int
     * @author: huaqiang.liu
     * @date: 2021/6/3 17:59
     * @since JDK 1.8
     */
    public int updateOtherMergeSubOrderSubmitFlag(String dealDtlNo, String notifySubmitFlag, Date oldUpdateDtm, String mainDealOrderNo) {
        Date now = new Date();
        int n = highDealOrderDtlPoMapper.updateAppFlagOrSubmitFlag(dealDtlNo, null, notifySubmitFlag, null, null, oldUpdateDtm, now);
        // 更新其他子订单
        if (n > 0) {
            highDealOrderDtlPoMapper.updateOtherMergeSubOrderSubmitFlag(dealDtlNo, notifySubmitFlag, now, mainDealOrderNo);
        }
        return n;
    }

    /**
     * getUnEcontractOrderDtl:查询未签订电子合同的高端订单明细信息
     *
     * @param startDt 开始日期时间
     * @return List<HighDealOrderDtlPo>
     * <AUTHOR>
     * @date 2017年4月20日 上午10:31:49
     */
    public List<HighDealOrderDtlPo> getUnEcontractOrderDtl(String startDt) {
        return highDealOrderDtlPoMapper.selectUnEcontractOrderDtl(startDt);
    }

    /**
     * 查询柜台未生成电子合同订单
     *
     * @param counterConfigs
     * @param startDt
     * @return java.util.List<com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo>
     * @author: huaqiang.liu
     * @date: 2021/4/15 10:31
     * @since JDK 1.8
     */
    public List<HighDealOrderDtlPo> getCounterUnEcontractOrderDtl(List<CustEcontractCfgVo> counterConfigs, String startDt) {
        return highDealOrderDtlPoMapper.selectCounterUnEcontractOrderDtl(counterConfigs, startDt);
    }

    /***
     *
     * getCountHighFundDealOrderDtlForConsole:(根据条件统计高端公墓交易订单明细)
     *
     * @param queryCondition
     * @return
     * <AUTHOR>
     * @date 2017年7月19日 上午11:27:16
     */
    public HighDealOrderDtlPo getCountHighFundDealOrderDtlForConsole(QueryHighFundDealOrderDtlCondition queryCondition) {
        ConsoleHighFundDealOrderDtlCondition condition = new ConsoleHighFundDealOrderDtlCondition();
        BeanUtils.copyProperties(queryCondition, condition);
        Date appDateStart = null;
        Date appDateEnd = null;
        if (queryCondition.getAppDateStart() != null) {
            appDateStart = DateUtils.formatToDate(queryCondition.getAppDateStart(), DateUtils.YYYYMMDD);
        }
        if (queryCondition.getAppDateEnd() != null) {
            appDateEnd = DateUtils.addDay(DateUtils.formatToDate(queryCondition.getAppDateEnd(), DateUtils.YYYYMMDD), 1);
        }
        return highDealOrderDtlPoMapper.selectCountHighDealOrderDtlForConsole(condition, appDateStart, appDateEnd, queryCondition.getTaTradeDtStart(),
                queryCondition.getTaTradeDtEnd());
    }

    /***
     *
     * getHighFundDealOrderDtlForConsole:(根据条件查询私募交易订单明细)
     *
     * @param queryCondition
     * @param pageNo
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2017年7月19日 上午11:26:43
     */
    public Page<ConsoleHighFundDealOrderDtlVo> getHighFundDealOrderDtlForConsole(QueryHighFundDealOrderDtlCondition queryCondition, Integer pageNo,
                                                                                 Integer pageSize) {
        if (pageNo != null && pageSize != null) {
            PageHelper.startPage(pageNo, pageSize);
        }

        ConsoleHighFundDealOrderDtlCondition condition = new ConsoleHighFundDealOrderDtlCondition();
        BeanUtils.copyProperties(queryCondition, condition);

        Date appDateStart = null;
        Date appDateEnd = null;
        if (queryCondition.getAppDateStart() != null) {
            appDateStart = DateUtils.formatToDate(queryCondition.getAppDateStart(), DateUtils.YYYYMMDD);
        }
        if (queryCondition.getAppDateEnd() != null) {
            appDateEnd = DateUtils.addDay(DateUtils.formatToDate(queryCondition.getAppDateEnd(), DateUtils.YYYYMMDD), 1);
        }

        return highDealOrderDtlPoMapper.selectHighDealOrderDtlForConsole(condition, appDateStart, appDateEnd, queryCondition.getTaTradeDtStart(),
                queryCondition.getTaTradeDtEnd());
    }

    /**
     * updateTaTradeDt:更新订单明细TA工作日
     *
     * @param dealDtlNo     订单明细号
     * @param newSubmitTaDt 新上报TA工作日
     * @param oldSubmitTaDt 原上报TA工作日
     * @param mBusiCode     中台业务码
     * @return int
     * <AUTHOR>
     * @date 2017年7月28日 上午10:52:59
     */
    public int updateSubmitTaDt(String dealDtlNo, String newSubmitTaDt, String oldSubmitTaDt, String mBusiCode) {
        return highDealOrderDtlPoMapper.updateSubmitTaDt(dealDtlNo, newSubmitTaDt, oldSubmitTaDt, mBusiCode);
    }

    /**
     * countNotNotify:统计当前交易日，是否存在未通知的订单数量
     *
     * @param taTradeDt
     * @return
     * <AUTHOR>
     * @date 2017年9月5日 下午1:16:10
     */
    public Integer countNotNotify(String taTradeDt, String productChannel) {
        return highDealOrderDtlPoMapper.countNotNotify(taTradeDt, productChannel);
    }

    /**
     * countDealOrderDtl:查询订单明细数量
     *
     * @param dealNo
     * @return
     * <AUTHOR>
     * @date 2018年5月14日 下午5:24:12
     */
    public int countDealOrderDtl(String dealNo) {
        return highDealOrderDtlPoMapper.countDealOrderDtl(dealNo);
    }

    /**
     * countUnAckShareMergeDealOrder:查询未确认的份额合并/迁移订单
     *
     * @param startDtm
     * @param taTradeDt
     * @return
     * <AUTHOR>
     * @date 2018年5月14日 下午5:24:12
     */
    public int countUnAckShareMergeDealOrder(Date startDtm, String taTradeDt) {
        return highDealOrderDtlPoMapper.countUnAckShareMergeDealOrder(startDtm, taTradeDt);
    }

    /**
     * updateStateOfNotify:(更新订单明细上报通知标识)
     *
     * @param dealDtlNo        订单明细号
     * @param txAppFlag        交易申请标识
     * @param notifySubmitFlag 上报通知标识
     * @param oldUpdateDtm     原更新时间
     * @param isForceUpdate    是否强制更新
     * <AUTHOR>
     * @date 2018年5月18日 下午3:11:36
     */
    public void updateStateOfNotify(String dealDtlNo, String txAppFlag, String notifySubmitFlag, Date oldUpdateDtm, boolean isForceUpdate) {
        if (StringUtils.isEmpty(txAppFlag) && StringUtils.isEmpty(notifySubmitFlag)) {
            return;
        }

        Date updateDtm = null;
        if (!isForceUpdate) {
            updateDtm = oldUpdateDtm;
        }

        int affectNum = highDealOrderDtlPoMapper.updateStateOfNotify(dealDtlNo, notifySubmitFlag, txAppFlag, updateDtm, new Date(), null, null);

        if (0 == affectNum) {
            throw new BusinessException(ExceptionCodes.BATCH_CENTER_PAYMENT_ORDER_STATE_NOTIFY_FLAG_FAILED,
                    MessageSource.getMessageByCode(ExceptionCodes.BATCH_CENTER_PAYMENT_ORDER_STATE_APP_FLAG_FAILED));
        }
    }

    /**
     * updateStateOfNotify:更新申请状态或者通知上报状态
     *
     * @param dealDtlNo        交易订单明细号
     * @param txAppFlag        交易申请状态
     * @param notifySubmitFlag 申请上报状态
     * @param oldUpdateDtm     最后更新时间戳
     * @param isForceUpdate    是否强制更新（不带最后更新时间戳），true：强制更新，false：带时间戳更新
     * @param calmDtm          冷静期
     * @param cancelSrc        撤单来源
     * @return void
     * <AUTHOR>
     * @date 2016年10月11日 上午12:23:32
     */
    public void updateStateOfNotify(String dealDtlNo, String txAppFlag, String notifySubmitFlag, Date oldUpdateDtm, boolean isForceUpdate,
                                    Date calmDtm, String cancelSrc) {

        if (StringUtils.isEmpty(txAppFlag)) {
            return;
        }

        Date updateDtm = null;
        if (!isForceUpdate) {
            updateDtm = oldUpdateDtm;
        }

        int affectNum = 0;
        // 更新的高端订单明细
        affectNum = highDealOrderDtlPoMapper.updateStateOfNotify(dealDtlNo, notifySubmitFlag, txAppFlag, updateDtm, new Date(), calmDtm, cancelSrc);

        if (0 == affectNum) {
            throw new BusinessException(ExceptionCodes.BATCH_CENTER_PAYMENT_ORDER_STATE_NOTIFY_FLAG_FAILED,
                    MessageSource.getMessageByCode(ExceptionCodes.BATCH_CENTER_PAYMENT_ORDER_STATE_APP_FLAG_FAILED));
        }
    }

    /**
     * updateRetrieveDtm:(更新订单检索时间)
     *
     * @param dealDtlNo
     * @param retrieveDtm
     * @param oldRetrieveDtm
     * <AUTHOR>
     * @date 2018年5月29日 下午5:41:39
     */
    public void updateRetrieveDtm(String dealDtlNo, Date retrieveDtm, Date oldRetrieveDtm, Date oldUpdateDtm) {
        // 更新的高端订单明细
        int affectNum = highDealOrderDtlPoMapper.updateRetrieveDtm(dealDtlNo, retrieveDtm, oldRetrieveDtm, oldUpdateDtm);

        if (0 == affectNum) {
            throw new BusinessException(ExceptionCodes.BATCH_CENTER_PAYMENT_ORDER_STATE_NOTIFY_FLAG_FAILED,
                    MessageSource.getMessageByCode(ExceptionCodes.BATCH_CENTER_PAYMENT_ORDER_STATE_APP_FLAG_FAILED));
        }
    }

    /**
     * queryComplInfoList:(查询合规订单列表)
     *
     * @param fundCode 产品代码
     * @param startDt  开始日期
     * @param endDt    结束日期
     * @return
     * <AUTHOR>
     * @date 2018年5月25日 下午3:10:22
     */
    public List<ComplInfoDealVo> queryCompleteInfoList(String fundCode, String startDt, String endDt) {
        return highDealOrderDtlPoMapper.selectComplInfoList(fundCode, startDt, endDt);
    }

    /**
     * getNeedRefreshNotifyOrder:(查询所有需要更新通知状态的订单明细记录)
     *
     * @param now
     * @return
     * <AUTHOR>
     * @date 2018年5月29日 下午1:41:19
     */
    public List<HighDealOrderDtlPo> getNeedRefreshNotifyOrder(Date now) {
        return highDealOrderDtlPoMapper.selectNeedRefreshNotifyOrder(now);
    }


    /**
     * queryListForTradeLimit:确认结果额度处理
     *
     * @param tradeDt
     * @param taCodeList
     * @return
     * <AUTHOR>
     * @date 2018年5月29日 下午5:40:06
     */
    public List<HighDealOrderDtlPo> queryListForTradeLimit(String tradeDt, List<String> taCodeList) {
        return highDealOrderDtlPoMapper.queryListForTradeLimit(tradeDt, taCodeList);
    }

    /**
     * getNotDualentryAndCallback:查询未双录未回访的交易
     *
     * @param submitTaDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午1:58:31
     */
    public List<HighDealOrderDtlPo> getNotDualentryAndCallback(String submitTaDt, List<String> taCodes) {
        return highDealOrderDtlPoMapper.selectNotDualentryAndCallback(submitTaDt, taCodes);
    }

    /**
     * getNotPassCalm:查询未过冷静期的交易
     *
     * @param submitTaDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午1:58:36
     */
    public List<HighDealOrderDtlPo> getNotPassCalm(String submitTaDt, List<String> taCodes, Date date) {
        return highDealOrderDtlPoMapper.selectNotPassCalm(submitTaDt, taCodes, date);
    }

    /**
     * getInvalidAssetcertificate:查询无效资产证明的交易
     *
     * @param submitTaDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午1:58:40
     */
    public List<HighDealOrderDtlPo> getInvalidAssetcertificate(String submitTaDt, List<String> taCodes) {
        return highDealOrderDtlPoMapper.selectInvalidAssetcertificate(submitTaDt, taCodes);
    }

    /**
     * countNotNeedNotify:统计需要通知上报但状态仍然为无需上报的交易
     *
     * @param submitTaDt
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午4:58:39
     */
    public Integer countNotNeedNotify(String submitTaDt, String productChannel, Date date) {
        return highDealOrderDtlPoMapper.countNotNeedNotify(submitTaDt, productChannel, date);
    }

    /**
     * getListNotNeedNotify:查询无需通知数据
     *
     * @param submitTaDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2018年6月5日 上午10:43:04
     */
    public List<HighDealOrderDtlPo> getListNotNeedNotify(String submitTaDt, List<String> taCodes, Date date) {
        return highDealOrderDtlPoMapper.selectNotNeedNotify(submitTaDt, taCodes, date);
    }

    /**
     * countNotNotifyOrReNotify:统计未通知或重新通知的交易
     *
     * @param submitTaDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午4:59:48
     */
    public Integer countNotNotifyOrReNotify(String submitTaDt, List<String> taCodes) {
        return highDealOrderDtlPoMapper.countNotNotifyOrReNotify(submitTaDt, taCodes);
    }

    public List<String> queryNotNotifyOrReNotify(String submitTaDt, List<String> taCodes) {
        return highDealOrderDtlPoMapper.queryNotNotifyOrReNotify(submitTaDt, taCodes);
    }

    /**
     * getListNotNotifyOrReNotify:查询未通知or重新通知的交易
     *
     * @param submitTaDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2018年6月5日 上午10:43:30
     */
    public List<HighDealOrderDtlPo> getListUnNotifyOrReNotify(String submitTaDt, List<String> taCodes) {
        return highDealOrderDtlPoMapper.selectUnNotifyOrReNotify(submitTaDt, taCodes);
    }

    /**
     * updateInterposeFlag:(更新干预标识)
     *
     * @param dealDtlNo              订单明细号
     * @param assetInterposeFlag     资产证明干预标识
     * @param dualentryInterposeFlag 双录干预标识
     * @param callbackInterposeFlag  回访干预标识
     * @param calmdtmInterposeFlag   冷静期干预标识
     * @param now                    更新时间
     * @return
     * <AUTHOR>
     * @date 2018年6月5日 下午7:42:37
     */
    public int updateInterposeFlag(String dealDtlNo, String assetInterposeFlag, String dualentryInterposeFlag, String callbackInterposeFlag, String calmdtmInterposeFlag, Date now) {

        return highDealOrderDtlPoMapper.updateInterposeFlag(dealDtlNo, assetInterposeFlag, dualentryInterposeFlag, callbackInterposeFlag, calmdtmInterposeFlag, now);
    }

    /**
     * queryAllNeedSyncSubmitDtDealList:(查询所有需要同步上报TA日期的订单)
     *
     * @param productCode
     * @param submitTaDt
     * @return
     * <AUTHOR>
     * @date 2018年6月8日 上午10:38:27
     */
    public Page<HighDealOrderDtlPo> queryAllNeedSyncSubmitDtDealList(String productCode, String submitTaDt, String mBusiCode, Integer pageNum, Integer pageSize) {
        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }

        return highDealOrderDtlPoMapper.selectAllNeedSyncSubmitDtDealList(productCode, submitTaDt, mBusiCode);
    }

    /**
     * getHighDealDtlInterposeVo:查询干预列表
     *
     * @param queryCondition
     * @param pageNo
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2018年6月7日 下午1:22:22
     */
    public Page<HighDealDtlInterposeVo> getHighDealDtlInterposeVo(QueryHighDealDtlInterposeCondition queryCondition, Integer pageNo, Integer pageSize) {
        if (pageNo != null && pageSize != null) {
            PageHelper.startPage(pageNo, pageSize);
        }

        HighDealDtlInterposeCondition condition = new HighDealDtlInterposeCondition();
        BeanUtils.copyProperties(queryCondition, condition);

        return highDealOrderDtlPoMapper.selectHighDealDtlInterposeVo(condition);
    }


    /**
     * updateComplInfo:更新双录回访
     *
     * @param po
     * @return
     * <AUTHOR>
     * @date 2018年7月11日 下午3:35:28
     */
    public int updateComplInfo(HighDealOrderDtlPo po) {
        return highDealOrderDtlPoMapper.updateComplInfo(po);
    }

    /**
     * @param po
     * @return int
     * @description:更新合并订单的双录回访
     * @author: haiguang.chen
     * @date: 2021/9/10 10:09
     * @since JDK 1.8
     */
    public int updateMergeComplInfo(HighDealOrderDtlPo po) {
        return highDealOrderDtlPoMapper.updateMergeComplInfo(po);
    }

    /**
     * 更新撤单日期
     *
     * @param dealDtlNo
     * @param tradeDt
     */
    public int updateRefundDtByDealDtlNo(String dealDtlNo, String tradeDt) {
        return highDealOrderDtlPoMapper.updateRefundDtByDealDtlNo(dealDtlNo, tradeDt);
    }

    /**
     * getHighExpireRedeemReportVo:滚动赎回报表
     *
     * @param taCode
     * @param fundCode
     * @param fundName
     * @param isRedeemExpire
     * @param preExpireStartDate
     * @param preExpireEndDate
     * @param pageNo
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2018年10月16日 下午1:26:16
     */
    public Page<HighExpireRedeemReportVo> getHighExpireRedeemReportVo(String txAcctNo, String taCode, String fundCode, String fundName, String isRedeemExpire,
                                                                      String preExpireStartDate, String preExpireEndDate, Integer pageNo, Integer pageSize) {
        if (pageNo != null && pageSize != null) {
            PageHelper.startPage(pageNo, pageSize);
        }

        return highDealOrderDtlPoMapper.selectHighExpireRedeemReportVo(txAcctNo, taCode, fundCode, fundName, isRedeemExpire, preExpireStartDate, preExpireEndDate);
    }

    /**
     * updateExpireInfo:更新到期信息
     *
     * @param dealNo
     * @param isRedeemExpire
     * @param preExpireDate
     * @return
     * <AUTHOR>
     * @date 2018年10月16日 下午2:23:24
     */
    public int updateExpireInfo(String dealNo, String isRedeemExpire, String preExpireDate) {
        return highDealOrderDtlPoMapper.updateExpireInfo(dealNo, isRedeemExpire, preExpireDate);
    }

    /**
     * countNotSubmit:统计未上报的数量
     *
     * @param submitTaDt
     * @return
     * <AUTHOR>
     * @date 2018年9月19日 下午4:44:49
     */
    public Integer countNotSubmit(String submitTaDt, String taCode, String productChannel) {
        return highDealOrderDtlPoMapper.countNotSubmit(submitTaDt, taCode, productChannel);
    }

    /**
     * queryNotSubmitDtlList:查询上报明细
     *
     * @param submitTaDt
     * @param taCode
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年9月20日 上午9:58:15
     */
    public List<HighDealSubmitDtlVo> queryNotSubmitDtlList(String submitTaDt, String taCode, String productChannel) {
        return highDealOrderDtlPoMapper.selectNotSubmitDtl(submitTaDt, taCode, productChannel);
    }

    /**
     * countPurchase:预约报表购买部分
     *
     * @param submitTaDt
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2018年9月27日 下午4:55:44
     */
    public AppointmentReportVo countPurchase(String submitTaDt, String fundCode) {
        return highDealOrderDtlPoMapper.countPurchase(submitTaDt, fundCode);
    }

    /**
     * @param submitTaDt
     * @param fundCode
     * @return com.howbuy.tms.high.batch.dao.vo.AppointmentReportVo
     * @Description 预约报表购买部分, 购买人数
     * <AUTHOR>
     * @Date 2018/12/13 18:21
     **/
    public AppointmentReportVo countPurchasePeoples(String submitTaDt, String fundCode) {
        return highDealOrderDtlPoMapper.countPurchasePeoples(submitTaDt, fundCode);
    }

    /**
     * countRedeem:预约报表赎回部分
     *
     * @param submitTaDt
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2018年9月27日 下午4:56:00
     */
    public AppointmentReportVo countRedeem(String submitTaDt, String fundCode) {
        return highDealOrderDtlPoMapper.countRedeem(submitTaDt, fundCode);
    }

    /**
     * @param submitTaDt
     * @param fundCode
     * @return com.howbuy.tms.high.batch.dao.vo.AppointmentReportVo
     * @Description 预约报表赎回部分, 赎回人数
     * <AUTHOR>
     * @Date 2018/12/13 18:21
     **/
    public AppointmentReportVo countRedeemPeoples(String submitTaDt, String fundCode) {
        return highDealOrderDtlPoMapper.countRedeemPeoples(submitTaDt, fundCode);
    }

    /**
     * countPurchase:预约报表购买部分
     *
     * @param list
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2018年9月27日 下午4:55:44
     */
    public List<AppointmentDetailVo> selectPurchase(List<String> list, String fundCode) {
        return highDealOrderDtlPoMapper.selectPurchase(list, fundCode);
    }

    /**
     * countRedeem:预约报表赎回部分
     *
     * @param list
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2018年9月27日 下午4:56:00
     */
    public List<AppointmentDetailVo> selectRedeem(List<String> list, String fundCode) {
        return highDealOrderDtlPoMapper.selectRedeem(list, fundCode);
    }

    /**
     * getNotDualentryForReport:查询未双录
     *
     * @param submitTaStartDt
     * @param submitTaEndDt
     * @param payDeadLineDt
     * @param txAcctNo
     * @return
     * <AUTHOR>
     * @date 2018年9月28日 下午4:11:41
     */
    public List<HighTradeReportCheckVo> getNotDualentryForReport(String submitTaStartDt, String submitTaEndDt, String payDeadLineDt, List<String> fundCodeList,
                                                                 String txAcctNo, String taCode, String payDeadLineEndDt) {
        return highDealOrderDtlPoMapper.selectNotDualentryForReport(submitTaStartDt, submitTaEndDt, payDeadLineDt, fundCodeList, txAcctNo, taCode, payDeadLineEndDt);
    }

    /**
     * getNotCallback:查询未回访的
     *
     * @param submitTaStartDt
     * @param submitTaEndDt
     * @param payDeadLineDt
     * @param txAcctNo
     * @return
     * <AUTHOR>
     * @date 2018年9月28日 下午4:11:27
     */
    public List<HighTradeReportCheckVo> getNotCallbackForReport(String submitTaStartDt, String submitTaEndDt, String payDeadLineDt, List<String> fundCodeList,
                                                                String txAcctNo, String taCode) {
        return highDealOrderDtlPoMapper.selectNotCallbackForReport(submitTaStartDt, submitTaEndDt, payDeadLineDt, fundCodeList, txAcctNo, taCode);
    }

    /**
     * getNotPassCalmForReport:查询未过冷静期的交易
     *
     * @param submitTaStartDt
     * @param submitTaEndDt
     * @param payDeadLineDt
     * @param txAcctNo
     * @param date
     * @param taCode
     * @return
     * <AUTHOR>
     * @date 2018年9月28日 下午4:11:19
     */
    public List<HighTradeReportCheckVo> getNotPassCalmForReport(String submitTaStartDt, String submitTaEndDt, String payDeadLineDt, List<String> fundCodeList,
                                                                String txAcctNo, Date date, String taCode) {
        return highDealOrderDtlPoMapper.selectNotPassCalmForReport(submitTaStartDt, submitTaEndDt, payDeadLineDt, fundCodeList, txAcctNo, date, taCode);
    }

    /**
     * getInvalidAssetcertificateForReport:查询无效资产证明的交易
     *
     * @param submitTaStartDt
     * @param submitTaEndDt
     * @param payDeadLineDt
     * @param txAcctNo
     * @return
     * <AUTHOR>
     * @date 2018年9月28日 下午4:11:06
     */
    public List<HighTradeReportCheckVo> getInvalidAssetcertificateForReport(String submitTaStartDt, String submitTaEndDt, String payDeadLineDt, List<String> fundCodeList,
                                                                            String txAcctNo, String taCode, String payDeadLineEndDt) {
        return highDealOrderDtlPoMapper.selectInvalidAssetcertificateForReport(submitTaStartDt, submitTaEndDt, payDeadLineDt, fundCodeList, txAcctNo, taCode, payDeadLineEndDt);
    }

    /**
     * getPartRedeemReport:查询部分赎回报表
     *
     * @param submitTaStartDt
     * @param submitTaEndDt
     * @param taCode
     * @param fundCode
     * @param txAcctNo
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年10月8日 上午10:12:33
     */
    public List<HighRedeemVo> getPartRedeemReport(String submitTaStartDt, String submitTaEndDt, String taCode, String fundCode, String txAcctNo, String productChannel) {
        return highDealOrderDtlPoMapper.selectPartRedeemReport(submitTaStartDt, submitTaEndDt, taCode, fundCode, txAcctNo, productChannel);
    }

    /**
     * getLargeRedeemReport:巨额赎回报表
     *
     * @param submitTaStartDt
     * @param submitTaEndDt
     * @param taCode
     * @param fundCode
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年10月8日 下午1:53:35
     */
    public List<HighRedeemVo> getLargeRedeemReport(String submitTaStartDt, String submitTaEndDt, String taCode, String fundCode, String productChannel) {
        return highDealOrderDtlPoMapper.selectLargeRedeemReport(submitTaStartDt, submitTaEndDt, taCode, fundCode, productChannel);
    }

    public List<HighRedeemVo> getPartRedeemReportList(String submitTaStartDt, String submitTaEndDt,
                                                      List<String> taCodeList, List<String> fundCodeList, String txAcctNo,
                                                      String productChannel, String disCode) {
        return highDealOrderDtlPoMapper.selectPartRedeemReportList(submitTaStartDt, submitTaEndDt, taCodeList, fundCodeList, txAcctNo, productChannel, disCode);
    }


    public List<HighRedeemVo> getLargeRedeemReportList(String submitTaStartDt, String submitTaEndDt, List<String> taCodeList,
                                                       List<String> fundCodeList, String productChannel) {
        return highDealOrderDtlPoMapper.selectLargeRedeemReportList(submitTaStartDt, submitTaEndDt, taCodeList, fundCodeList, productChannel);
    }


    /**
     * getByFundCodeAndSubmitTaDt:根据产品代码和开放区间查订单
     *
     * @param submitTaStartDt
     * @param submitTaEndDt
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2018年10月10日 下午1:31:27
     */
    public List<ConsoleHighFundDealOrderDtlVo> getByFundCodeAndSubmitTaDt(String submitTaStartDt, String submitTaEndDt, String fundCode) {
        return highDealOrderDtlPoMapper.selectByFundCodeAndSubmitTaDt(submitTaStartDt, submitTaEndDt, fundCode);
    }

    /**
     * getByDealNoListAndTaCodeList:根据订单号列表和TA列表查询
     *
     * @param taCodes
     * @param dealNos
     * @return
     * <AUTHOR>
     * @date 2018年12月6日 下午4:00:39
     */
    public List<String> getByDealNoListAndTaCodeList(List<String> taCodes, List<String> dealNos) {
        return highDealOrderDtlPoMapper.selectByDealNoListAndTaCodeList(taCodes, dealNos);
    }

    public List<String> getTaByDealNoListAndTaCodeList(List<String> taCodes, List<String> dealNos) {
        return highDealOrderDtlPoMapper.getTaByDealNoListAndTaCodeList(taCodes, dealNos);
    }

    public List<HighDealOrderDtlPo> getRepurchaseDeals(String repurchaseProtocolNo) {
        return highDealOrderDtlPoMapper.selectRepurchaseDeals(repurchaseProtocolNo);
    }

    public List<HighDealOrderDtlPo> getRepurchaseDealsByNos(List<String> repurchaseProtocolNoList) {
        return highDealOrderDtlPoMapper.selectRepurchaseDealsByNos(repurchaseProtocolNoList);
    }

    public List<HighDealOrderDtlPo> getByDealDtlNoList(Set<String> dealDtlNos) {
        return highDealOrderDtlPoMapper.selectByDealDtlNoList(dealDtlNos);
    }

    public List<HighDealOrderDtlPo> getByDealDtlNoListWithDb(Set<String> dealDtlNos) {
        return highDealOrderDtlPoMapper.selectByDealDtlNoList(dealDtlNos);
    }

    public List<HighDealDtlChangeCardVo> getDtlChangeCardByDealDtlNoList(Set<String> dealDtlNos) {
        return highDealOrderDtlPoMapper.selectDtlChangeCardByDealDtlNoList(dealDtlNos);
    }

    public Page<CustInvestDealVo> queryCustInvestDeal(QueryCustInvestConditonVo conditon) {
        PageHelper.startPage(conditon.getPageNo(), conditon.getPageSize());

        return highDealOrderDtlPoMapper.selectCustInvestDeals(conditon);
    }

    public List<HighDealOrderDtlPo> getRedeemOnWayDeal(List<String> txAcctNoList, String taTradeDt) {
        return highDealOrderDtlPoMapper.selectRedeemOnWayDeal(txAcctNoList, taTradeDt);
    }

    /**
     * updateByDealDtlNo:根据订单号更新订单明细信息
     *
     * @param cxgDealNo param orderDtlNo
     * @return
     * <AUTHOR>
     * @date 2017年7月7日 下午2:42:27
     */
    public int updateByDealDtlNoByCxgDealNo(String cxgDealNo, String orderDtlNo) {
        return highDealOrderDtlPoMapper.updateByDealDtlNoByCxgDealNo(cxgDealNo, orderDtlNo);
    }

    /**
     * @param productCode
     * @param submitTaDt
     * @param submitTaDt
     * @param pageNo
     * @param pageSize
     * @return com.github.pagehelper.Page<com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo>
     * @Description 查询未上报交易
     * <AUTHOR>
     * @Date 2020/7/15 16:15
     **/
    public Page<UnSubmitHighDealVo> queryUnSumitDealBySubmitDt(String productCode, String submitTaDt, String mBusiCode, int pageNo, int pageSize) {
        PageHelper.startPage(pageNo, pageSize);

        return highDealOrderDtlPoMapper.selectUnSumitDealBySubmitDt(productCode, submitTaDt, mBusiCode);
    }

    /**
     * @param taCodes
     * @param submitTaDt
     * @param submitTaDt
     * @param pageNo
     * @param pageSize
     * @return com.github.pagehelper.Page<com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo>
     * @Description 根据TAcode查询未上报交易
     * <AUTHOR>
     * @Date 2020/7/15 16:15
     **/
    public Page<UnSubmitHighDealVo> queryUnSumitDealByTacodes(List<String> taCodes, String submitTaDt, int pageNo, int pageSize) {
        PageHelper.startPage(pageNo, pageSize);

        return highDealOrderDtlPoMapper.selectUnSumitDealByTacodes(taCodes, submitTaDt);
    }


    /**
     * @param taCodes
     * @param pmtDt
     * @param pageNo
     * @param pageSize
     * @return com.github.pagehelper.Page<com.howbuy.tms.high.batch.dao.vo.UnSubmitHighDealVo>
     * @Description 查询未支付订单
     * <AUTHOR>
     * @Date 2020/8/18 15:00
     **/
    public Page<UnPmtDtHighDealVo> queryUnPmtDealByTacodes(List<String> taCodes, String pmtDt, int pageNo, int pageSize) {
        PageHelper.startPage(pageNo, pageSize);

        return highDealOrderDtlPoMapper.selectUnPayByTacodes(taCodes, pmtDt);
    }

    /**
     * 查询认缴金额信息
     *
     * @param fundCodes
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.SubsAmtInfoVo>
     * @author: huaqiang.liu
     * @date: 2020/9/30 15:50
     * @since JDK 1.8
     */
    public List<SubsAmtInfoVo> queryCustSubsAmtInfo(List<String> fundCodes) {
        return highDealOrderDtlPoMapper.queryCustSubsAmtInfo(fundCodes);
    }

    /**
     * 修改认申购单上的认缴金额
     *
     * @param txAcctNo
     * @param fundCode
     * @param subsAmt
     * @return void
     * @author: huaqiang.liu
     * @date: 2020/10/13 16:02
     * @since JDK 1.8
     */
    public void updateSubsAmt(String txAcctNo, String fundCode, BigDecimal subsAmt) {
        highDealOrderDtlPoMapper.updateSubsAmt(txAcctNo, fundCode, subsAmt);
    }

    /**
     * 查询客户订单明细
     *
     * @param params
     * @return java.util.List<com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo>
     * @author: huaqiang.liu
     * @date: 2021/1/14 9:41
     * @since JDK 1.8
     */
    public List<HighDealOrderDtlPo> queryCustRedeemOrderDtl(HighDealOrderDtlPo params) {
        return highDealOrderDtlPoMapper.selectCustRedeemOrderDtl(params);
    }

    /**
     * 统计订单表上报单数量
     *
     * @param mainDealNo
     * @return int
     * @author: huaqiang.liu
     * @date: 2021/4/9 10:32
     * @since JDK 1.8
     */
    public int countDealOrderMergeSubmitNum(String mainDealNo) {
        return highDealOrderDtlPoMapper.countDealOrderMergeSubmitNum(mainDealNo);
    }

    /**
     * 更新认申购订单的参与日期
     *
     * @param vo
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/4/9 17:11
     * @since JDK 1.8
     */
    public void updateBuyOrderJoinDt(SelectRedeemSplitSubCustBookVo vo) {
        highDealOrderDtlPoMapper.updateBuyOrderJoinDt(vo);
    }

    /**
     * 根据主订单号查询订单列表
     *
     * @param mainDealNo
     * @return java.util.List<com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo>
     * @author: huaqiang.liu
     * @date: 2021/4/9 13:24
     * @since JDK 1.8
     */
    public List<HighDealOrderDtlPo> getOrdersByMainDealNo(String mainDealNo) {
        return highDealOrderDtlPoMapper.selectOrdersByMainDealNo(mainDealNo);
    }


    /**
     * 查询单条订单明细（机构对账）
     *
     * @param externalDealNo
     * @param dealNo
     * @param txAcctNo
     * @param disCode
     * @return com.howbuy.tms.high.batch.dao.vo.OtcHighDealOrderDtlVo
     * @author: huaqiang.liu
     * @date: 2021/4/26 16:03
     * @since JDK 1.8
     */
    public OtcHighDealOrderDtlVo queryHighDealOrderDtlForOtc(String externalDealNo, String dealNo, String txAcctNo, String disCode) {
        return highDealOrderDtlPoMapper.selectHighDealOrderDtlForOtc(externalDealNo, dealNo, txAcctNo, disCode);
    }

    /**
     * 统计未支付成功的子订单数量
     *
     * @param mainDealNo
     * @return int
     * @author: huaqiang.liu
     * @date: 2021/6/8 10:22
     * @since JDK 1.8
     */
    public int countNotPaySuccessSubOrders(String mainDealNo) {
        return highDealOrderDtlPoMapper.countNotPaySuccessSubOrders(mainDealNo);
    }

    /**
     * 查询全部订单明细（机构对账）
     *
     * @param taTradeDt
     * @param disCode
     * @param taCode
     * @param mBusiCode
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.OtcHighDealOrderDtlVo>
     * @author: huaqiang.liu
     * @date: 2021/4/26 16:03
     * @since JDK 1.8
     */
    public List<OtcHighDealOrderDtlVo> queryHighDealOrderDtlForOtcAll(String taTradeDt, String disCode, String taCode, String mBusiCode) {
        return highDealOrderDtlPoMapper.selectHighDealOrderDtlForOtcAll(taTradeDt, disCode, taCode, mBusiCode);
    }

    /**
     * @param tradeDtStart
     * @param tradeDtEnd
     * @param productIds
     * @return com.github.pagehelper.Page<com.howbuy.tms.high.batch.dao.vo.HighFundArrivalProofRecordVo>
     * @description:(查询需要生成资金到账证明文件的订单)
     * @author: haiguang.chen
     * @date: 2022/4/27 14:27
     * @since JDK 1.8
     */
    public Page<HighFundArrivalProofRecordVo> getLimitedCooperativeProductOrders(String tradeDtStart, String tradeDtEnd, List<String> productIds) {
        return highDealOrderDtlPoMapper.selectLimitedCooperativeProductOrders(tradeDtStart, tradeDtEnd, productIds);

    }

    public BigDecimal getPeDivideCallAmt(String txAcctNo, String fundCode, String taTradeDt) {
        return highDealOrderDtlPoMapper.selectPeDivideCallAmt(txAcctNo, fundCode, taTradeDt);
    }


    /**
     * @param fundCode
     * @param submitTaDateBegin
     * @param submitTaDateEnd
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.AppointmentReportVo>
     * @description:(查询非预约的购买数据)
     * @author: haiguang.chen
     * @date: 2022/5/5 9:20
     * @since JDK 1.8
     */
    public List<AppointmentReportVo> countPurchaseForNotAppoint(String fundCode, String submitTaDateBegin, String submitTaDateEnd) {
        return highDealOrderDtlPoMapper.countPurchaseForNotAppoint(fundCode, submitTaDateBegin, submitTaDateEnd);
    }

    /**
     * @param fundCode
     * @param submitTaDateBegin
     * @param submitTaDateEnd
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.AppointmentDetailVo>
     * @description:(查询非预约的购买数据Dtl)
     * @author:
     * @date:
     * @since JDK 1.8
     */
    public List<AppointmentDetailVo> countPurchaseForNotAppointDtl(String fundCode, String submitTaDateBegin, String submitTaDateEnd) {
        return highDealOrderDtlPoMapper.countPurchaseForNotAppointDtl(fundCode, submitTaDateBegin, submitTaDateEnd);
    }

    /**
     * @param fundCode
     * @param submitTaDateBegin
     * @param submitTaDateEnd
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.AppointmentReportVo>
     * @description:(查询非预约的赎回数据)
     * @author: haiguang.chen
     * @date: 2022/5/5 9:20
     * @since JDK 1.8
     */
    public List<AppointmentReportVo> countRedeemForNotAppoint(String fundCode, String submitTaDateBegin, String submitTaDateEnd) {
        return highDealOrderDtlPoMapper.countRedeemForNotAppoint(fundCode, submitTaDateBegin, submitTaDateEnd);
    }

    /**
     * @param fundCode
     * @param submitTaDateBegin
     * @param submitTaDateEnd
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.AppointmentDetailVo>
     * @description:(查询非预约的赎回数据Dtl)
     * @author:
     * @date:
     * @since JDK 1.8
     */
    public List<AppointmentDetailVo> countRedeemForNotAppointDtl(String fundCode, String submitTaDateBegin, String submitTaDateEnd) {
        return highDealOrderDtlPoMapper.countRedeemForNotAppointDtl(fundCode, submitTaDateBegin, submitTaDateEnd);
    }

    /**
     * @param submitTaDate
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.HighTradeReportCheckVo>
     * @description:(查询未回访数据)
     * @author: haiguang.chen
     * @date: 2022/5/9 13:41
     * @since JDK 1.8
     */
    public List<HighTradeReportCheckVo> getNotCallBackForReport(String submitTaDate) {
        return highDealOrderDtlPoMapper.getNotCallBackForReport(submitTaDate);
    }

    /**
     * @param dealDtlNo
     * @return com.howbuy.tms.high.batch.dao.vo.HighAppointInfoVo
     * @description:(查询订单信息)
     * @author: haiguang.chen
     * @date: 2022/6/29 17:16
     * @since JDK 1.8
     */
    public HighAppointInfoVo getAppointInfoByDealDtlNo(String dealDtlNo) {
        return highDealOrderDtlPoMapper.getAppointInfoByDealDtlNo(dealDtlNo);
    }

    /**
     * 根据资金出款消息，更新dtl表出款时间、出款日期
     *
     * @param ackDt
     * @param refundDt
     * @param payDt
     * @param orderPayStatus
     * @param mBusiCodeList
     * @param txAppFlagList
     * @param dealDtlNo
     * @param orgSerialNo
     * @return
     */
    public int updatePayOutStatusAndDtByOSerialNo(String ackDt, String refundDt, String payDt, String orderPayStatus,
                                                  List<String> mBusiCodeList, List<String> txAppFlagList, String dealDtlNo, String orgSerialNo) {
        if (StringUtils.isEmpty(dealDtlNo) && StringUtils.isEmpty(orgSerialNo)) {
            return 0;
        }
        if (StringUtils.isEmpty(ackDt) && StringUtils.isEmpty(refundDt)) {
            return 0;
        }
        return highDealOrderDtlPoMapper.updatePayOutStatusAndDtByOSerialNo(ackDt, refundDt, payDt, orderPayStatus, mBusiCodeList, txAppFlagList, dealDtlNo, orgSerialNo);
    }

    /**
     * 根据确认日期查询
     *
     * @param ackDt
     * @return
     */
    public List<HighDealOrderDtlPo> getByAckDt(String ackDt) {
        return highDealOrderDtlPoMapper.selectByAckDt(ackDt);
    }

    /**
     * 在途交易客户
     *
     * @param fundCodes
     * @return
     */
    public List<HighDealOrderDtlPo> getOnWays(List<String> fundCodes) {
        return highDealOrderDtlPoMapper.selectOnWays(fundCodes);
    }

    /**
     * 获取订单明细及私募对账订单
     *
     * @param dealDtlNos
     * @return
     */
    public List<HighDealDtlAndCheckOrderPo> getHighDealDtlAndCheckOrder(List<String> dealDtlNos, String ackDt, String continuanceFlag, String taCode) {
        if (CollectionUtil.isEmpty(dealDtlNos) && ackDt == null && continuanceFlag == null) {
            throw new BusinessException(ExceptionCodes.PARAM_IS_NULL,
                    MessageSource.getMessageByCode(ExceptionCodes.PARAM_IS_NULL));
        }

        return highDealOrderDtlPoMapper.selectHighDealDtlAndCheckOrder(dealDtlNos, ackDt, continuanceFlag, taCode);
    }

    public int updatePayOutStatusAndDtForSplit(String payDt, String orderPayStatus, String orgSerialNo) {
        if (StringUtils.isEmpty(orgSerialNo)) {
            return 0;
        }
        return highDealOrderDtlPoMapper.updatePayOutStatusAndDtForSplit(payDt, orderPayStatus, orgSerialNo);

    }

    public HighOnWayAmtInfoVo queryOnWayAmtInfo(String txAcctNo, String productCode) {
        return highDealOrderDtlPoMapper.queryOnWayAmtInfo(txAcctNo, productCode);
    }

    public int updateStageFlag(List<String> fundCodes, String ackDt) {
        return highDealOrderDtlPoMapper.updateStageFlag(fundCodes, ackDt);
    }

    public List<HighDealDtlAndCheckOrderPo> getAllRedeemSplitOrder(String tradeDt, String taCode) {
        return highDealOrderDtlPoMapper.getAllRedeemSplitOrder(tradeDt, taCode);
    }

    public SpecialProductOrderPo getSpecialProductOrder(String dealNo) {
        return highDealOrderDtlPoMapper.getSpecialProductOrder(dealNo);
    }


    /**
     * 根据example查询交易记录
     */
    public List<HighDealOrderDtlPo> getByExample(HighDealOrderDtlPoExample highDealOrderDtlPoExample) {
        List<HighDealOrderDtlPo> list = highDealOrderDtlPoMapper.selectByExample(highDealOrderDtlPoExample);
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() : list;
    }

    public List<HighDealOrderDtlPo> queryOwnershipOrderByParam(QueryOwnershipRightTransferParam param) {
        // 审核中的信息在另一个数据库,无法跨库分页查询,所以不分页,默认查询前1000条
        PageHelper.startPage(1, 10000);
        QueryOwnershipRightTransferVo queryOwnershipRightTransferVo = new QueryOwnershipRightTransferVo();
        BeanUtils.copyProperties(param, queryOwnershipRightTransferVo);
        Page<HighDealOrderDtlPo> highDealOrderDtlPos = highDealOrderDtlPoMapper.queryOwnershipRightTransferOrder(queryOwnershipRightTransferVo);
        List<HighDealOrderDtlPo> result = highDealOrderDtlPos.getResult();
        return CollectionUtils.isEmpty(result) ? new ArrayList<>() : result;
    }


    /**
     * 更新
     */
    public void updateByPrimaryKeySelective(HighDealOrderDtlPo highDealOrderDtlPo) {
        HighDealOrderDtlPoExample example = new HighDealOrderDtlPoExample();
        example.createCriteria().andDealDtlNoEqualTo(highDealOrderDtlPo.getDealDtlNo());
        highDealOrderDtlPoMapper.updateByExampleSelective(highDealOrderDtlPo, example);
    }

    public List<HighDealOrderDtlPo> selectRedeemDealOrder(String tradeDt) {
        return highDealOrderDtlPoMapper.selectRedeemDealOrder(tradeDt);
    }

    public HighDealOrderDtlPo queryByOriginSerialNo(String originSerialNo) {
        HighDealOrderDtlPoExample example = new HighDealOrderDtlPoExample();
        example.createCriteria().andOriginSerialNoEqualTo(originSerialNo);
        List<HighDealOrderDtlPo> highDealOrderDtlPos = highDealOrderDtlPoMapper.selectByExample(example);
        return CollectionUtils.isEmpty(highDealOrderDtlPos) ? null : highDealOrderDtlPos.get(0);
    }

    public void updateCxgDealNoByOriginSerialNo(String cxgOrderNo, String busiDealNo) {
        highDealOrderDtlPoMapper.updateCxgDealNoByOriginSerialNo(cxgOrderNo, busiDealNo);
    }

    public void updateCxgDealNoByDealNo(String dealNo, String cxgOrderNo) {
        highDealOrderDtlPoMapper.updateCxgDealNoByDealNo(dealNo, cxgOrderNo);
    }

    public List<HighDealOrderDtlPo> selectByDealDtlNoList(List<String> dealDtlNoList) {
        HighDealOrderDtlPoExample example = new HighDealOrderDtlPoExample();
        example.createCriteria().andDealDtlNoIn(dealDtlNoList);
        return highDealOrderDtlPoMapper.selectByExample(example);

    }

    /**
     * 找出最早一笔确认成功的认申购订单明细
     *
     * @param txAcctNo 交易账号
     * @param fundCode 产品编码
     * @return
     */
    public HighDealOrderDtlPo selectSubsPurOrderByHbOneNoAndFundCode(String txAcctNo, String fundCode, String disCode) {
        // 1.找出所有有确认份额的认申购订单
        List<String> busiCodeList = new ArrayList<>();
        busiCodeList.add(BusinessCodeEnum.SUBS.getMCode());
        busiCodeList.add(BusinessCodeEnum.PURCHASE.getMCode());
        HighDealOrderDtlPoExample example = new HighDealOrderDtlPoExample();
        example.createCriteria().andTxAcctNoEqualTo(txAcctNo).andFundCodeEqualTo(fundCode).andDisCodeEqualTo(disCode).andMBusiCodeIn(busiCodeList).andAckVolGreaterThan(BigDecimal.ZERO);
        List<HighDealOrderDtlPo> highDealOrderDtlPoList = highDealOrderDtlPoMapper.selectByExample(example);
        // 2.找出确认时间最早的一笔
        if (CollectionUtils.isEmpty(highDealOrderDtlPoList)) {
            return null;
        }
        highDealOrderDtlPoList = highDealOrderDtlPoList.stream().sorted(Comparator.comparing(HighDealOrderDtlPo::getAckDt)).collect(Collectors.toList());
        return highDealOrderDtlPoList.get(0);
    }

    public List<HighDealOrderDtlPo> selectNoTradeOrderList(String txAcctNo, String fundCode, String taTradeDt) {
        List<String> mBusiCodeList = new ArrayList<>();
        mBusiCodeList.add(BusinessCodeEnum.NOTRADE_OVERACCOUNT_IN.getMCode());
        mBusiCodeList.add(BusinessCodeEnum.NOTRADE_OVERACCOUNT_OUT.getMCode());
        HighDealOrderDtlPoExample example = new HighDealOrderDtlPoExample();
        example.createCriteria().andTxAcctNoEqualTo(txAcctNo).andFundCodeEqualTo(fundCode).andTaTradeDtLessThanOrEqualTo(taTradeDt).andMBusiCodeIn(mBusiCodeList);
        return highDealOrderDtlPoMapper.selectByExample(example);
    }

    public List<MergeSubmitOrderVo> getMergeSubmitOrderList(List<String> mainDealNos) {
        return highDealOrderDtlPoMapper.selectMergeSubmitOrderList(mainDealNos);
    }

    public void updateUnpaymentAppFlagToFailByPmtCheckDt(String tradeDt, String taCode) {
        highDealOrderDtlPoMapper.updateUnpaymentAppFlagToFailByPmtCheckDt(tradeDt, taCode);
    }


    public int updateSyncSubmitTaDt(String dealDtlNo, String oldSubmitTaDt, String newSubmitTaDt, Date oldUpdateDtm, Date newUpdateDtm) {
        return highDealOrderDtlPoMapper.updateSyncSubmitTaDt(dealDtlNo, oldSubmitTaDt, newSubmitTaDt, oldUpdateDtm, newUpdateDtm);
    }

    public int updateSubmitFlagWithForce(String dealNo, HighDealOrderDtlPo highDealOrderDtlPo) {
        return highDealOrderDtlPoMapper.updateSubmitFlagWithForce(dealNo, highDealOrderDtlPo);
    }

    public int updateNotifySubmitFlagByDealDtlNo(HighDealOrderDtlPo orderDtl) {
        return highDealOrderDtlPoMapper.updateNotifySubmitFlagByDealDtlNo(orderDtl);
    }

    public List<HighDealOrderDtlPo> selectOrdersByMainDealNo(String mainDealOrderNo) {
        return highDealOrderDtlPoMapper.selectOrdersByMainDealNo(mainDealOrderNo);
    }

    public int execSyncSubmitTaDtBatch(List<String> dealNoList, String oldSubmitTaDt, String newSubmitTaDt, Date date) {
        return highDealOrderDtlPoMapper.execSyncSubmitTaDtBatch(dealNoList, oldSubmitTaDt, newSubmitTaDt, date);
    }

    public int updateFaceCancelNoNeedNotifyOtherMergeOrders(String mainDealOrderNo, String workDay, String code, Date now) {
        return highDealOrderDtlPoMapper.updateFaceCancelNoNeedNotifyOtherMergeOrders(mainDealOrderNo, workDay, code, now);
    }

    public List<AppACKFlagVo> selectAppACKFlag(String dealNo) {
        return highDealOrderDtlPoMapper.selectAppACKFlag(dealNo);
    }

    public int updateRedeemDirection(String dealNo, String redeemDirection) {
        return highDealOrderDtlPoMapper.updateRedeemDirection(dealNo, redeemDirection);
    }

    public int updateNotifyStatus(String dealDtlNo, String notifyFlag, String submitTaDt, String assetStatus, Date oldUpdateDtm, String oldSubmitTaDt, String oldNotifyFlag, String oldAssetStatus, Date oldRetrieveDtm, Date retrieveDtm) {
        return highDealOrderDtlPoMapper.updateNotifyStatus(dealDtlNo, notifyFlag, submitTaDt, assetStatus, oldUpdateDtm, oldSubmitTaDt, oldNotifyFlag, oldAssetStatus, oldRetrieveDtm, retrieveDtm);
    }

    public int updateInterposeFlagByDealNo(String dealNo, String assetInterposeFlag, String dualentryInterposeFlag, String callbackInterposeFlag, String calmdtmInterposeFlag, Date now, String submitTaDt, String submitTaDtOld, String submitTaDtInterposeFlag, Date retrieveDtm) {
        return highDealOrderDtlPoMapper.updateInterposeFlagByDealNo(dealNo, assetInterposeFlag, dualentryInterposeFlag, callbackInterposeFlag, calmdtmInterposeFlag, now, submitTaDt, submitTaDtOld, submitTaDtInterposeFlag, retrieveDtm);
    }

    public HighOnWayAmtInfoVo queryAllOnWayAmtInfo(String txAcctNo, String fundCode) {
        return highDealOrderDtlPoMapper.queryAllOnWayAmtInfo(txAcctNo, fundCode);
    }

    public void updateAllSubsAmt(String txAcctNo, String fundCode, BigDecimal totalSubsAmt) {
        highDealOrderDtlPoMapper.updateAllSubsAmt(txAcctNo, fundCode, totalSubsAmt);
    }

    public List<HighDealReportVo> queryDealList(String submitTaDt, String fundCode, String mBusiCode) {
        return highDealOrderDtlPoMapper.queryDealList(submitTaDt, fundCode, mBusiCode);
    }

    public List<HighDealOrderDtlPo> selectByDealNoList(List<String> dealNoList) {
        HighDealOrderDtlPoExample example = new HighDealOrderDtlPoExample();
        example.createCriteria().andDealNoIn(dealNoList);
        return highDealOrderDtlPoMapper.selectByExample(example);
    }

    /**
     * 根据taCode查询所有不重复的产品代码
     *
     * @param taCode TA代码
     * @return 产品代码列表
     * <AUTHOR>
     * @date 2025/01/15 10:30
     * @since JDK 1.8
     */
    public List<String> selectDistinctFundCodesByTaCode(String taCode) {
        return highDealOrderDtlPoMapper.selectDistinctFundCodesByTaCode(taCode);
    }

    public List<HighDealOrderDtlPo> querySubmitCurrentBuyDealOrder(String workDay) {
        // 认申购
        List<String> mBusiCodeList = new ArrayList<>();
        mBusiCodeList.add(BusinessCodeEnum.SUBS.getMCode());
        mBusiCodeList.add(BusinessCodeEnum.PURCHASE.getMCode());

        List<String> notifySubmitFlagList = new ArrayList<>();
        notifySubmitFlagList.add(NotifySubmitFlagEnum.NO_NOTIFY.getCode());
        notifySubmitFlagList.add(NotifySubmitFlagEnum.RE_NOTIFY.getCode());
        HighDealOrderDtlPoExample example = new HighDealOrderDtlPoExample();
        example.createCriteria().andSubmitTaDtEqualTo(workDay).andMBusiCodeIn(mBusiCodeList).andNotifySubmitFlagIn(notifySubmitFlagList);
        return highDealOrderDtlPoMapper.selectByExample(example);
    }

    public List<HighDealOrderDtlPo> queryNotSubmitBuyDealOrder(String workDay) {
        // 认申购
        List<String> mBusiCodeList = new ArrayList<>();
        mBusiCodeList.add(BusinessCodeEnum.SUBS.getMCode());
        mBusiCodeList.add(BusinessCodeEnum.PURCHASE.getMCode());
        List<String> notifySubmitFlagList = new ArrayList<>();
        notifySubmitFlagList.add(NotifySubmitFlagEnum.NO_NOTIFY.getCode());
        notifySubmitFlagList.add(NotifySubmitFlagEnum.RE_NOTIFY.getCode());
        HighDealOrderDtlPoExample example = new HighDealOrderDtlPoExample();
        example.createCriteria().andSubmitTaDtGreaterThan(workDay).andMBusiCodeIn(mBusiCodeList).andNotifySubmitFlagIn(notifySubmitFlagList);
        return highDealOrderDtlPoMapper.selectByExample(example);
    }

    public List<HighDealOrderDtlPo> queryAllConfirmBuyInOrder(String txAcctNo, String fundCode) {
        if (StringUtils.isBlank(txAcctNo) || StringUtils.isBlank(fundCode)) {
            return new ArrayList<>();
        }
        // 确认成功或者部分成功的申购、认购、红利再投、强增、非交易过户转入、转托管转入、份额迁移转入的订单
        List<String> mBusiCodeList = new ArrayList<>();
        mBusiCodeList.add(BusinessCodeEnum.SUBS.getMCode());
        mBusiCodeList.add(BusinessCodeEnum.PURCHASE.getMCode());
        mBusiCodeList.add(BusinessCodeEnum.FORCE_ADD.getMCode());
        mBusiCodeList.add(BusinessCodeEnum.NOTRADE_OVERACCOUNT_IN.getMCode());
        mBusiCodeList.add(BusinessCodeEnum.TRANS_MANAGE_IN.getMCode());
        mBusiCodeList.add(BusinessCodeEnum.FUND_SHARE_TRANSFER_IN.getMCode());
        HighDealOrderDtlPoExample example = new HighDealOrderDtlPoExample();
        List<String> txAckFlagList = new ArrayList<>();
        txAckFlagList.add(TxAckFlagEnum.CONFIRM_SUCCESS.getCode());
        txAckFlagList.add(TxAckFlagEnum.PART_CONFIRMED.getCode());
        example.createCriteria().andTxAcctNoEqualTo(txAcctNo).andFundCodeEqualTo(fundCode).andMBusiCodeIn(mBusiCodeList).andTxAckFlagIn(txAckFlagList);
        List<HighDealOrderDtlPo> buyOrderList = highDealOrderDtlPoMapper.selectByExample(example);
        // 红利再投确认订单
        HighDealOrderDtlPoExample divExample = new HighDealOrderDtlPoExample();
        divExample.createCriteria().andTxAcctNoEqualTo(txAcctNo).andFundCodeEqualTo(fundCode).andMBusiCodeEqualTo(BusinessCodeEnum.DIV.getMCode()).andTxAckFlagIn(txAckFlagList).andFundDivModeEqualTo(FundDivModeEnum.BONUS_INVEST.getCode());
        List<HighDealOrderDtlPo> divOrderList = highDealOrderDtlPoMapper.selectByExample(divExample);
        // 合并订单
        List<HighDealOrderDtlPo> orderList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(buyOrderList)){
            orderList.addAll(buyOrderList);
        }
        if(CollectionUtils.isNotEmpty(divOrderList)){
            orderList.addAll(divOrderList);
        }
        return orderList;
    }
}
