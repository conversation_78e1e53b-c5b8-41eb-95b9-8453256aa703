package com.howbuy.tms.high.batch.service.event.fund05file;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.high.batch.service.event.HighEventListener;
import com.howbuy.tms.high.batch.service.repository.High05FileRecRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description: 05文件IO完成事件监听器
 * <AUTHOR>
 * @date 2025/01/15 10:30
 * @since JDK 1.8
 */
@Component
@Slf4j
public class Fund05FileIoCompleteEventListener extends HighEventListener<Fund05FileIoCompleteEvent> {

    @Autowired
    private High05FileRecRepository high05FileRecRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processEvent(Fund05FileIoCompleteEvent event) {
        log.info("Fund05FileIoCompleteEventListener-接收到05文件IO完成事件, event:{}", JSON.toJSONString(event));

        try {
            String fundCode = event.getFundCode();
            String taTradeDt = event.getTaTradeDt();

            // 删除正式表中已存在的数据
            int deletedCount = high05FileRecRepository.deleteByFundCodeAndImportDt(fundCode, taTradeDt);
            log.info("Fund05FileIoCompleteEventListener-删除正式表已存在数据, fundCode:{}, taTradeDt:{}, deletedCount:{}",
                fundCode, taTradeDt, deletedCount);

            // 使用INSERT INTO SELECT FROM方式将IO表数据迁移到正式表
            int insertedCount = high05FileRecRepository.insertFromIoTable(fundCode, taTradeDt);
            log.info("Fund05FileIoCompleteEventListener-成功迁移{}条05文件记录到正式表, fundCode:{}, taTradeDt:{}",
                insertedCount, fundCode, taTradeDt);

        } catch (Exception e) {
            log.error("Fund05FileIoCompleteEventListener-处理05文件IO完成事件失败, event:{}, error:", JSON.toJSONString(event), e);
            throw e;
        }
    }
}
