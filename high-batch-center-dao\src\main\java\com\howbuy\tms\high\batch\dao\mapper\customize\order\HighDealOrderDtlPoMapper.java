package com.howbuy.tms.high.batch.dao.mapper.customize.order;

import com.github.pagehelper.Page;
import com.howbuy.tms.high.batch.dao.mapper.order.HighDealOrderDtlPoAutoMapper;
import com.howbuy.tms.high.batch.dao.po.batch.SimuFundCheckOrderDto;
import com.howbuy.tms.high.batch.dao.po.order.*;
import com.howbuy.tms.high.batch.dao.vo.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;
/**
 * 去O
 */
public interface HighDealOrderDtlPoMapper extends HighDealOrderDtlPoAutoMapper {

    /**
     * updateUnpaymentAppFlagToFailByPmtCheckDt:根据TA工作日将未支付的订单明细的交易申请标志设置为申请失败
     *
     * @param pmtCheckDt
     * @param taCode
     * @return
     * <AUTHOR>
     * @date 2017年7月7日 下午7:03:44
     */
    int updateUnpaymentAppFlagToFailByPmtCheckDt(@Param("pmtCheckDt") String pmtCheckDt, @Param("taCode") String taCode);

    /**
     * selectByDealNo:根据主订单号查询交易高端订单明细列表信息
     *
     * @param dealNo 主订单编号
     * @return List<FundDealOrderDtlPo>
     * <AUTHOR>
     * @date 2016年11月2日 下午10:08:52
     */
    List<HighDealOrderDtlPo> selectByDealNo(@Param("dealNo") String dealNo);

    /**
     * selectDealOrdersToNotify:( 查询未通知/需重新通知的订单明细列表)
     *
     * @param tradeDt
     * @return
     * <AUTHOR>
     * @date 2018年6月20日 下午4:22:46
     */
    List<SimuFundCheckOrderDto> selectDealOrdersToNotify(@Param("tradeDt") String tradeDt);

    /**
     * 查询未通知/需重新通知的子订单明细列表
     *
     * @param dealNo
     * @return java.util.List<com.howbuy.tms.high.batch.dao.po.batch.SimuFundCheckOrderDto>
     * @author: huaqiang.liu
     * @date: 2021/3/11 15:50
     * @since JDK 1.8
     */
    List<SimuFundCheckOrderDto> selectMergeSubmitOrdersToNotify(@Param("dealNo") String dealNo);

    /**
     * 查询待上报交易记录
     * @return 交易记录
     */
    List<WaitSubmitOrderDto> queryWaitSubmitDealOrderDtl(@Param("endDate") Date endDate);

    /**
     * updateNotifySubmitFlagByDealDtlNo:根据主键更新通知状态
     *
     * @param record
     * @return
     * <AUTHOR>
     * @date 2016年9月19日 下午6:07:23
     */
    int updateNotifySubmitFlagByDealDtlNo(HighDealOrderDtlPo record);

    /**
     * updateAppFlagOrSubmitFlag:更新订单明细中的交易申请状态或通知上报标记
     *
     * @param dealDtlNo
     * @param txAppFlag
     * @param notifySubmitFlag
     * @param cancelOrderSrc
     * @param memo
     * @param oldUpdateDtm
     * @param now
     * @return
     * <AUTHOR>
     * @date 2017年7月7日 下午1:40:40
     */
    int updateAppFlagOrSubmitFlag(@Param("dealDtlNo") String dealDtlNo, @Param("txAppFlag") String txAppFlag,
                                  @Param("notifySubmitFlag") String notifySubmitFlag, @Param("cancelOrderSrc") String cancelOrderSrc, @Param("memo") String memo,
                                  @Param("oldUpdateDtm") Date oldUpdateDtm, @Param("now") Date now);

    /**
     * 更新交易订单明细中的通知上报状态
     *
     * @param dealDtlNo
     * @param notifySubmitFlag
     * @param now
     * @param mainDealOrderNo
     * @return int
     * @author: huaqiang.liu
     * @date: 2021/6/3 18:04
     * @since JDK 1.8
     */
    int updateOtherMergeSubOrderSubmitFlag(@Param("dealDtlNo") String dealDtlNo, @Param("notifySubmitFlag") String notifySubmitFlag,
                                           @Param("now") Date now, @Param("mainDealOrderNo") String mainDealOrderNo);

    /***
     *
     * selectCountHighDealOrderDtlForConsole:(中控台-统计全部申请金额/份额)
     *
     * @param condition
     * @param appDateStart
     * @param appDateEnd
     * @param taTradeDtStart
     * @param taTradeDtEnd
     * @return
     * <AUTHOR>
     * @date 2017年7月19日 上午11:18:15
     */
    HighDealOrderDtlPo selectCountHighDealOrderDtlForConsole(@Param("condition") ConsoleHighFundDealOrderDtlCondition condition,
                                                             @Param("appDateStart") Date appDateStart, @Param("appDateEnd") Date appDateEnd, @Param("taTradeDtStart") String taTradeDtStart,
                                                             @Param("taTradeDtEnd") String taTradeDtEnd);

    /**
     * updateAppFlagAndTaTradeDt:按需，更新txAppFlag与taTradeDt
     *
     * @param dealDtlNo
     * @param txAppFlag
     * @param taTradeDt
     * @param oldTxAppFlag
     * @param cancelOrderSrc
     * @param now
     * @return
     * <AUTHOR>
     * @date 2016年10月13日 下午2:00:49
     */
    int updateAppFlagAndTaTradeDt(@Param("dealDtlNo") String dealDtlNo, @Param("txAppFlag") String txAppFlag, @Param("taTradeDt") String taTradeDt,
                                  @Param("oldTxAppFlag") String oldTxAppFlag, @Param("cancelOrderSrc") String cancelOrderSrc, @Param("now") Date now);

    /***
     *
     * selectHighDealOrderDtlForConsole:(中控台-查询高端订单明细)
     *
     * @param condition
     * @param appDateStart
     * @param appDateEnd
     * @param taTradeDtStart
     * @param taTradeDtEnd
     * @return
     * <AUTHOR>
     * @date 2017年7月19日 上午11:28:12
     */
    Page<ConsoleHighFundDealOrderDtlVo> selectHighDealOrderDtlForConsole(@Param("condition") ConsoleHighFundDealOrderDtlCondition condition,
                                                                         @Param("appDateStart") Date appDateStart, @Param("appDateEnd") Date appDateEnd, @Param("taTradeDtStart") String taTradeDtStart,
                                                                         @Param("taTradeDtEnd") String taTradeDtEnd);

    /**
     * selectUnEcontractOrderDtl:查询未签订电子合同的高端订单明细信息
     *
     * @param startDt 开始日期时间
     * @return List<HighDealOrderDtlPo>
     * <AUTHOR>
     * @date 2017年4月20日 上午10:29:53
     */
    List<HighDealOrderDtlPo> selectUnEcontractOrderDtl( @Param("startDt") String startDt);

    /**
     * 查询柜台未生成电子合同订单
     *
     * @param counterConfigs
     * @param startDt
     * @return java.util.List<com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo>
     * @author: huaqiang.liu
     * @date: 2021/4/15 10:33
     * @since JDK 1.8
     */
    List<HighDealOrderDtlPo> selectCounterUnEcontractOrderDtl(@Param("counterConfigs") List<CustEcontractCfgVo> counterConfigs, @Param("startDt") String startDt);

    /**
     * updateTaTradeDt:更新订单明细TA工作日
     *
     * @param dealDtlNo     订单明细号
     * @param newSubmitTaDt 新上报TA日
     * @param oldSubmitTaDt 原上报TA日
     * @param mBusiCode     中台业务码
     * @return int
     * <AUTHOR>
     * @date 2017年8月1日 下午5:14:28
     */
    int updateSubmitTaDt(@Param("dealDtlNo") String dealDtlNo, @Param("newSubmitTaDt") String newSubmitTaDt, @Param("oldSubmitTaDt") String oldSubmitTaDt,
                         @Param("mBusiCode") String mBusiCode);

    /**
     * countNotNotify:统计当前交易日，是否存在未通知的订单数量
     *
     * @param taTradeDt
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2017年8月3日 下午3:59:30
     */
    int countNotNotify(@Param("taTradeDt") String taTradeDt, @Param("productChannel") String productChannel);

    /**
     * selectHighForceDealOrderForConsole:(查询高端强制撤单列表)
     *
     * @param condition
     * @param appDateStart
     * @param appDateEnd
     * @param taTradeDtStart
     * @param taTradeDtEnd
     * @return
     * <AUTHOR>
     * @date 2017年11月14日 下午4:54:04
     */
    Page<CondoleHighForceDealOrderVo> selectHighForceDealOrderForConsole(@Param("condition") ConsoleHighForceDealOrderConditionVo condition,
                                                                         @Param("appDateStart") Date appDateStart, @Param("appDateEnd") Date appDateEnd, @Param("taTradeDtStart") String taTradeDtStart,
                                                                         @Param("taTradeDtEnd") String taTradeDtEnd);

    /**
     * updateSubmitFlagWithForce:(更新强制取消订单明细的上报标识)
     *
     * @param dealNo
     * @param highDealOrderDtlPo
     * @return
     * <AUTHOR>
     * @date 2017年11月21日 下午2:00:28
     */
    int updateSubmitFlagWithForce(@Param("dealNo") String dealNo, @Param("highDealOrderDtl") HighDealOrderDtlPo highDealOrderDtlPo);

    /**
     * countDealOrderDtl:根据订单号查询订单明细数量
     *
     * @param dealNo
     * @return
     * <AUTHOR>
     * @date 2018年5月14日 下午5:26:23
     */
    int countDealOrderDtl(@Param("dealNo") String dealNo);

    /**
     * countDealOrderDtl:查询未确认的份额合并/迁移订单
     *
     * @param startDtm
     * @param taTradeDt
     * @return
     * <AUTHOR>
     * @date 2018年5月14日 下午5:26:23
     */
    int countUnAckShareMergeDealOrder(@Param("startDtm") Date startDtm, @Param("taTradeDt") String taTradeDt);

    /**
     * updateStateOfNotify:(更新高度订单明细上报通知标识)
     *
     * @param dealDtlNo        订单明细号
     * @param notifySubmitFlag 上报通知标识
     * @param txAppFlag        交易申请标识
     * @param oldUpdateDtm     原更新时间
     * @param now              当前时间
     * @param calmDtm          冷静期
     * @return
     * <AUTHOR>
     * @date 2018年5月18日 下午3:07:25
     */
    int updateStateOfNotify(@Param("dealDtlNo") String dealDtlNo, @Param("notifySubmitFlag") String notifySubmitFlag, @Param("txAppFlag") String txAppFlag,
                            @Param("oldUpdateDtm") Date oldUpdateDtm, @Param("now") Date now, @Param("calmDtm") Date calmDtm, @Param("cancelSrc") String cancelSrc);

    /**
     * 更新合并单其他订单的通知状态
     *
     * @param mainDealOrderNo
     * @param cancelOrderSrc
     * @param now
     * @return int
     * @author: huaqiang.liu
     * @date: 2021/6/7 17:29
     * @since JDK 1.8
     */
    int updateFaceCancelNoNeedNotifyOtherMergeOrders(@Param("mainDealOrderNo") String mainDealOrderNo, @Param("refundDt") String refundDt, @Param("cancelOrderSrc") String cancelOrderSrc, @Param("now") Date now);

    /**
     * selectComplInfoList:(查询合规订单列表)
     *
     * @param fundCode 产品代码
     * @return
     * <AUTHOR>
     * @date 2018年5月25日 下午3:12:55
     */
    List<ComplInfoDealVo> selectComplInfoList(@Param("fundCode") String fundCode, @Param("startDt") String startDt, @Param("endDt") String endDt);

    /**
     * selectNeedRefreshNotifyOrder:(查询所有需要更新通知状态的订单明细记录)
     *
     * @param now
     * @return
     * <AUTHOR>
     * @date 2018年5月29日 下午1:42:43
     */
    List<HighDealOrderDtlPo> selectNeedRefreshNotifyOrder(@Param("now") Date now);

    /**
     * updateRetrieveDtm:(更新订单检索时间)
     *
     * @param dealDtlNo
     * @param retrieveDtm
     * @param oldRetrieveDtm
     * @param oldUpdateDtm
     * @return
     * <AUTHOR>
     * @date 2018年5月29日 下午5:37:58
     */
    int updateRetrieveDtm(@Param("dealDtlNo") String dealDtlNo, @Param("retrieveDtm") Date retrieveDtm,
                          @Param("oldRetrieveDtm") Date oldRetrieveDtm, @Param("oldUpdateDtm") Date oldUpdateDtm);

    /**
     * updateNotifyStatus:(更新通知状态标识)
     *
     * @param dealDtlNo
     * @param notifyFlag
     * @param submitTaDt
     * @param assetStatus
     * @param oldUpdateDtm
     * @param oldSubmitTaDt
     * @param oldNotifyFlag
     * @param oldAssetStatus
     * @return
     * <AUTHOR>
     * @date 2018年5月29日 下午7:02:16
     */
    int updateNotifyStatus(@Param("dealDtlNo") String dealDtlNo, @Param("notifyFlag") String notifyFlag, @Param("submitTaDt") String submitTaDt,
                           @Param("assetStatus") String assetStatus, @Param("oldUpdateDtm") Date oldUpdateDtm, @Param("oldSubmitTaDt") String oldSubmitTaDt,
                           @Param("oldNotifyFlag") String oldNotifyFlag, @Param("oldAssetStatus") String oldAssetStatus, @Param("oldRetrieveDtm") Date oldRetrieveDtm,
                           @Param("retrieveDtm") Date retrieveDtm);


    /**
     * queryListForTradeLimit:确认结果额度处理
     *
     * @param tradeDt
     * @param taCodeList
     * @return
     * <AUTHOR>
     * @date 2018年5月29日 下午5:39:03
     */
    List<HighDealOrderDtlPo> queryListForTradeLimit(@Param("tradeDt") String tradeDt, @Param("taCodeList") List<String> taCodeList);

    /**
     * updateRedeemDirection:更新汇款方向
     *
     * @param dealNo
     * @param redeemDirection
     * @return
     * <AUTHOR>
     * @date 2018年5月30日 下午3:54:04
     */
    int updateRedeemDirection(@Param("dealNo") String dealNo, @Param("redeemDirection") String redeemDirection);

    /**
     * selectNotDualentryAndCallback:查询当日上报但未双录未回访的交易
     *
     * @param submitTaDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午1:20:43
     */
    List<HighDealOrderDtlPo> selectNotDualentryAndCallback(@Param("submitTaDt") String submitTaDt, @Param("taCodes") List<String> taCodes);

    /**
     * selectNotPassCalm:查询未过冷静期的交易
     *
     * @param submitTaDt
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午1:20:43
     */
    List<HighDealOrderDtlPo> selectNotPassCalm(@Param("submitTaDt") String submitTaDt, @Param("taCodes") List<String> taCodes, @Param("date") Date date);

    /**
     * selectInvalidAssetcertificate:查询无效资产证明的交易
     *
     * @param submitTaDt
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午1:20:43
     */
    List<HighDealOrderDtlPo> selectInvalidAssetcertificate(@Param("submitTaDt") String submitTaDt, @Param("taCodes") List<String> taCodes);

    /**
     * countNotNeedNotify:统计当日满足通知条件但状态为无需通知的订单
     *
     * @param submitTaDt
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午4:47:28
     */
    Integer countNotNeedNotify(@Param("submitTaDt") String submitTaDt, @Param("productChannel") String productChannel, @Param("date") Date date);

    /**
     * selectNotNeedNotify:查询无需通知数据
     *
     * @param submitTaDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2018年6月5日 上午10:37:42
     */
    List<HighDealOrderDtlPo> selectNotNeedNotify(@Param("submitTaDt") String submitTaDt, @Param("taCodes") List<String> taCodes, @Param("date") Date date);

    /**
     * countNotNotifyOrReNotify:统计当日通知状态为未通知或重新通知
     *
     * @param submitTaDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午4:48:27
     */
    Integer countNotNotifyOrReNotify(@Param("submitTaDt") String submitTaDt, @Param("taCodes") List<String> taCodes);

    List<String> queryNotNotifyOrReNotify(@Param("submitTaDt") String submitTaDt, @Param("taCodes") List<String> taCodes);

    /**
     * selectUnNotifyOrReNotify:查询未通知or重新通知数据
     *
     * @param submitTaDt
     * @param taCodes
     * @return
     * <AUTHOR>
     * @date 2018年6月5日 上午10:38:01
     */
    List<HighDealOrderDtlPo> selectUnNotifyOrReNotify(@Param("submitTaDt") String submitTaDt, @Param("taCodes") List<String> taCodes);

    /**
     * updateInterposeFlag:(更新干预标识)
     *
     * @param dealDtlNo              订单明细号
     * @param assetInterposeFlag     资产证明干预标识
     * @param dualentryInterposeFlag 双录干预标识
     * @param callbackInterposeFlag  回访干预标识
     * @param calmdtmInterposeFlag   冷静期干预标识
     * @return
     * <AUTHOR>
     * @date 2018年6月5日 下午7:44:54
     */
    int updateInterposeFlag(@Param("dealDtlNo") String dealDtlNo, @Param("assetInterposeFlag") String assetInterposeFlag, @Param("dualentryInterposeFlag") String dualentryInterposeFlag, @Param("callbackInterposeFlag") String callbackInterposeFlag, @Param("calmdtmInterposeFlag") String calmdtmInterposeFlag, @Param("updateDtm") Date updateDtm);

    /**
     * selectAllNeedSyncSubmitDtDealList:(查询所有需要同步上报TA日期的订单)
     *
     * @param productCode
     * @param submitTaDt
     * @return
     * <AUTHOR>
     * @date 2018年6月8日 上午10:40:55
     */
    Page<HighDealOrderDtlPo> selectAllNeedSyncSubmitDtDealList(@Param("productCode") String productCode, @Param("submitTaDt") String submitTaDt, @Param("mBusiCode") String mBusiCode);

    /**
     * updateSyncSubmitTaDt:(同步更新上报TA日期)
     *
     * @param dealDtlNo
     * @param oldSubmitTaDt
     * @param newSubmitTaDt
     * @param oldUpdateDtm
     * @param newUpdateDtm
     * @return
     * <AUTHOR>
     * @date 2018年6月8日 下午1:00:20
     */
    int updateSyncSubmitTaDt(@Param("dealDtlNo") String dealDtlNo, @Param("oldSubmitTaDt") String oldSubmitTaDt,
                             @Param("newSubmitTaDt") String newSubmitTaDt, @Param("oldUpdateDtm") Date oldUpdateDtm,
                             @Param("newUpdateDtm") Date newUpdateDtm);

    /**
     * updateSyncSubmitTaDt:(批量同步更新上报TA日期)
     *
     * @param dealNoList
     * @param newSubmitTaDt
     * @param newUpdateDtm
     * @return
     * <AUTHOR>
     * @date 2019-07-23
     */
    int execSyncSubmitTaDtBatch(@Param("dealNoList") List<String> dealNoList, @Param("oldSubmitTaDt") String oldSubmitTaDt,
                                @Param("newSubmitTaDt") String newSubmitTaDt, @Param("newUpdateDtm") Date newUpdateDtm);

    /**
     * selectHighDealDtlInterposeVo:查询干预列表
     *
     * @param condition
     * @return
     * <AUTHOR>
     * @date 2018年6月7日 上午10:32:44
     */
    Page<HighDealDtlInterposeVo> selectHighDealDtlInterposeVo(@Param("condition") HighDealDtlInterposeCondition condition);

    /**
     * updateInterposeFlag:(更新干预标识)
     *
     * @param assetInterposeFlag      资产证明干预标识
     * @param dualentryInterposeFlag  双录干预标识
     * @param callbackInterposeFlag   回访干预标识
     * @param calmdtmInterposeFlag    冷静期干预标识
     * @param newSubmitTaDt           新上报ta日期
     * @param oldSubmitTaDt           旧上报ta日期
     * @param submitTaDtInterposeFlag 上报ta日期干预标识
     * @return
     * <AUTHOR>
     * @date 2018年6月5日 下午7:44:54
     */
    int updateInterposeFlagByDealNo(@Param("dealNo") String dealNo, @Param("assetInterposeFlag") String assetInterposeFlag, @Param("dualentryInterposeFlag") String dualentryInterposeFlag, @Param("callbackInterposeFlag") String callbackInterposeFlag, @Param("calmdtmInterposeFlag") String calmdtmInterposeFlag, @Param("updateDtm") Date updateDtm, @Param("newSubmitTaDt") String newSubmitTaDt, @Param("oldSubmitTaDt") String oldSubmitTaDt, @Param("submitTaDtInterposeFlag") String submitTaDtInterposeFlag, @Param("retrieveDtm") Date retrieveDtm);

    /**
     * updateComplInfo:更新双录回访
     *
     * @param record
     * @return
     * <AUTHOR>
     * @date 2018年7月11日 下午3:34:17
     */
    int updateComplInfo(HighDealOrderDtlPo record);

    /**
     * @param record
     * @return int
     * @description:更新合并订单的双录回访
     * @author: haiguang.chen
     * @date: 2021/9/10 9:57
     * @since JDK 1.8
     */
    int updateMergeComplInfo(HighDealOrderDtlPo record);

    /**
     * 更新撤单日期
     *
     * @param dealDtlNo
     * @param tradeDt
     * @return
     */
    int updateRefundDtByDealDtlNo(@Param("dealDtlNo") String dealDtlNo, @Param("tradeDt") String tradeDt);

    /**
     * selectHighExpireRedeemReportVo:滚动赎回报表
     *
     * @param taCode
     * @param fundCode
     * @param fundName
     * @param isRedeemExpire
     * @param preExpireStartDate
     * @param preExpireEndDate
     * @return
     * <AUTHOR>
     * @date 2018年10月16日 下午1:24:24
     */
    Page<HighExpireRedeemReportVo> selectHighExpireRedeemReportVo(@Param("txAcctNo") String txAcctNo, @Param("taCode") String taCode, @Param("fundCode") String fundCode,
                                                                  @Param("fundName") String fundName, @Param("isRedeemExpire") String isRedeemExpire, @Param("preExpireStartDate") String preExpireStartDate,
                                                                  @Param("preExpireEndDate") String preExpireEndDate);

    /**
     * updateExpireInfo:更新到期赎回信息
     *
     * @param dealNo
     * @param isRedeemExpire
     * @param preExpireDate
     * @return
     * <AUTHOR>
     * @date 2018年10月16日 下午2:17:10
     */
    int updateExpireInfo(@Param("dealNo") String dealNo, @Param("isRedeemExpire") String isRedeemExpire, @Param("preExpireDate") String preExpireDate);

    /**
     * countNotSubmit:统计未上报的数量
     *
     * @param submitTaDt
     * @param taCode
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年9月19日 下午4:42:05
     */
    Integer countNotSubmit(@Param("submitTaDt") String submitTaDt, @Param("taCode") String taCode, @Param("productChannel") String productChannel);

    /**
     * selectSubmitDtl:上报明细查询
     *
     * @param submitTaDt
     * @param taCode
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年9月20日 上午9:55:53
     */
    List<HighDealSubmitDtlVo> selectNotSubmitDtl(@Param("submitTaDt") String submitTaDt, @Param("taCode") String taCode, @Param("productChannel") String productChannel);

    /**
     * countPurchase:预约报表购买部分
     *
     * @param submitTaDt
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2018年9月27日 下午4:54:21
     */
    AppointmentReportVo countPurchase(@Param("submitTaDt") String submitTaDt, @Param("fundCode") String fundCode);

    /**
     * @param submitTaDt
     * @param fundCode
     * @return com.howbuy.tms.high.batch.dao.vo.AppointmentReportVo
     * @Description 预约报表购买部分, 购买人数
     * <AUTHOR>
     * @Date 2018/12/13 18:21
     **/
    AppointmentReportVo countPurchasePeoples(@Param("submitTaDt") String submitTaDt, @Param("fundCode") String fundCode);

    /**
     * countPurchase:预约报表赎回部分
     *
     * @param submitTaDt
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2018年9月27日 下午4:54:21
     */
    AppointmentReportVo countRedeem(@Param("submitTaDt") String submitTaDt, @Param("fundCode") String fundCode);

    /**
     * @param submitTaDt
     * @param fundCode
     * @return com.howbuy.tms.high.batch.dao.vo.AppointmentReportVo
     * @Description 预约报表赎回部分, 赎回人数
     * <AUTHOR>
     * @Date 2018/12/13 18:21
     **/
    AppointmentReportVo countRedeemPeoples(@Param("submitTaDt") String submitTaDt, @Param("fundCode") String fundCode);

    /**
     * selectPurchase:预约报表购买部分
     *
     * @param list
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2018年9月27日 下午4:54:21
     */
    List<AppointmentDetailVo> selectPurchase(@Param("list") List<String> list, @Param("fundCode") String fundCode);

    /**
     * selectRedeem:预约报表赎回部分
     *
     * @param list
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2018年9月27日 下午4:54:21
     */
    List<AppointmentDetailVo> selectRedeem(@Param("list") List<String> list, @Param("fundCode") String fundCode);

    /**
     * selectNotDualentryForReport:查询当日上报但未双录的交易
     *
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午1:20:43
     */
    List<HighTradeReportCheckVo> selectNotDualentryForReport(@Param("submitTaStartDt") String submitTaStartDt, @Param("submitTaEndDt") String submitTaEndDt,
                                                             @Param("payDeadLineDt") String payDeadLineDt, @Param("fundCodeList") List<String> fundCodeList, @Param("txAcctNo") String txAcctNo,
                                                             @Param("taCode") String taCode, @Param("payDeadLineEndDt") String payDeadLineEndDt);

    /**
     * selectNotCallbackForReport:查询当日上报但未回访的交易
     *
     * @return
     * <AUTHOR>
     * @date 2018年9月28日 下午3:47:43
     */
    List<HighTradeReportCheckVo> selectNotCallbackForReport(@Param("submitTaStartDt") String submitTaStartDt, @Param("submitTaEndDt") String submitTaEndDt,
                                                            @Param("payDeadLineDt") String payDeadLineDt, @Param("fundCodeList") List<String> fundCodeList, @Param("txAcctNo") String txAcctNo,
                                                            @Param("taCode") String taCode);

    /**
     * selectNotPassCalmForReport:查询未过冷静期的交易
     *
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午1:20:43
     */
    List<HighTradeReportCheckVo> selectNotPassCalmForReport(@Param("submitTaStartDt") String submitTaStartDt, @Param("submitTaEndDt") String submitTaEndDt,
                                                            @Param("payDeadLineDt") String payDeadLineDt, @Param("fundCodeList") List<String> fundCodeList, @Param("txAcctNo") String txAcctNo, @Param("date") Date date,
                                                            @Param("taCode") String taCode);

    /**
     * selectInvalidAssetcertificateForReport:查询无效资产证明的交易
     *
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午1:20:43
     */
    List<HighTradeReportCheckVo> selectInvalidAssetcertificateForReport(@Param("submitTaStartDt") String submitTaStartDt,
                                                                        @Param("submitTaEndDt") String submitTaEndDt, @Param("payDeadLineDt") String payDeadLineDt, @Param("fundCodeList") List<String> fundCodeList,
                                                                        @Param("txAcctNo") String txAcctNo, @Param("taCode") String taCode, @Param("payDeadLineEndDt") String payDeadLineEndDt);

    /**
     * selectPartRedeemReport:部分赎回报表
     * <AUTHOR>
     * @date 2018年10月4日 下午4:03:29
     */
    List<HighRedeemVo> selectPartRedeemReport(@Param("submitTaStartDt") String submitTaStartDt, @Param("submitTaEndDt") String submitTaEndDt, @Param("taCode") String taCode, @Param("fundCode") String fundCode, @Param("txAcctNo") String txAcctNo, @Param("productChannel") String productChannel);

    List<HighRedeemVo> selectPartRedeemReportList(@Param("submitTaStartDt") String submitTaStartDt, @Param("submitTaEndDt") String submitTaEndDt, @Param("taCodeList") List<String> taCodeList, @Param("fundCodeList") List<String> fundCodeList, @Param("txAcctNo") String txAcctNo, @Param("productChannel") String productChannel, @Param("disCode") String disCode);

    /**
     * selectLargeRedeemReport:巨额赎回报表
     *
     * @param submitTaStartDt
     * @param submitTaEndDt
     * @param taCode
     * @param fundCode
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年10月8日 下午1:52:27
     */
    List<HighRedeemVo> selectLargeRedeemReport(@Param("submitTaStartDt") String submitTaStartDt, @Param("submitTaEndDt") String submitTaEndDt, @Param("taCode") String taCode, @Param("fundCode") String fundCode, @Param("productChannel") String productChannel);

    List<HighRedeemVo> selectLargeRedeemReportList(@Param("submitTaStartDt") String submitTaStartDt, @Param("submitTaEndDt") String submitTaEndDt, @Param("taCodeList") List<String> taCodeList, @Param("fundCodeList") List<String> fundCodeList, @Param("productChannel") String productChannel);

    /**
     * selectByFundCodeAndSubmitTaDt:根据产品代码和开放区间查订单明细
     * <AUTHOR>
     * @date 2018年10月10日 下午1:29:10
     */
    List<ConsoleHighFundDealOrderDtlVo> selectByFundCodeAndSubmitTaDt(@Param("submitStartDt") String submitStartDt, @Param("submitEndDt") String submitEndDt, @Param("fundCode") String fundCode);

    /**
     * selectByDealNoListAndTaCodeList:根据订单号列表和TA列表查询
     *
     * @param taCodes
     * @param dealNos
     * @return
     * <AUTHOR>
     * @date 2018年12月6日 下午3:59:01
     */
    List<String> selectByDealNoListAndTaCodeList(@Param("taCodes") List<String> taCodes, @Param("dealNos") List<String> dealNos);

    List<String> getTaByDealNoListAndTaCodeList(@Param("taCodes") List<String> taCodes, @Param("dealNos") List<String> dealNos);

    List<HighDealOrderDtlPo> selectByDealNoList(@Param("dealNos") List<String> dealNos);

    List<HighDealOrderDtlPo> selectRepurchaseDeals(@Param("repurchaseProtocolNo") String repurchaseProtocolNo);

    List<HighDealOrderDtlPo> selectRepurchaseDealsByNos(@Param("repurchaseProtocolNoList") List<String> repurchaseProtocolNoList);


    List<HighDealOrderDtlPo> selectByDealDtlNoList(@Param("dealDtlNos") Set<String> dealDtlNos);

    List<HighDealDtlChangeCardVo> selectDtlChangeCardByDealDtlNoList(@Param("dealDtlNos") Set<String> dealDtlNos);

    /**
     * desc: 查询投资经历记录
     *
     * @param:
     * @return:
     * @auther: ligang
     * @date: 2020/2/20 下午5:01
     */
    Page<CustInvestDealVo> selectCustInvestDeals(@Param("conditonVo") QueryCustInvestConditonVo conditonVo);


    /**
     * @param txAcctNoList
     * @param taTradeDt
     * @return java.util.List<com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo>
     * @Description 赎回在途订单
     * <AUTHOR>
     * @Date 2020/3/31 15:59
     **/
    List<HighDealOrderDtlPo> selectRedeemOnWayDeal(@Param("txAcctNoList") List<String> txAcctNoList, @Param("taTradeDt") String taTradeDt);

    /**
     * 根据明细订单号更新储蓄罐订单号到明细表
     *
     * @param cxgDealNo
     * @param orderDtlNo
     * @return
     */
    int updateByDealDtlNoByCxgDealNo(@Param("cxgDealNo") String cxgDealNo, @Param("orderDtlNo") String orderDtlNo);

    /**
     * @param productCode
     * @param submitTaDt
     * @return com.github.pagehelper.Page<com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo>
     * @Description 查询未上报交易
     * <AUTHOR>
     * @Date 2020/7/15 16:16
     **/
    Page<UnSubmitHighDealVo> selectUnSumitDealBySubmitDt(@Param("productCode") String productCode, @Param("submitTaDt") String submitTaDt, @Param("mBusiCode") String mBusiCode);

    /**
     * @param taCodes
     * @param submitTaDt
     * @return com.github.pagehelper.Page<com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo>
     * @Description 根据TACode查询未上报交易
     * <AUTHOR>
     * @Date 2020/7/15 16:16
     **/
    Page<UnSubmitHighDealVo> selectUnSumitDealByTacodes(@Param("taCodes") List<String> taCodes, @Param("submitTaDt") String submitTaDt);

    /**
     * @param taCodes
     * @param pmtDt
     * @return com.github.pagehelper.Page<com.howbuy.tms.high.batch.dao.vo.UnSubmitHighDealVo>
     * @Description 查询未支付订单
     * <AUTHOR>
     * @Date 2020/8/18 15:02
     **/
    Page<UnPmtDtHighDealVo> selectUnPayByTacodes(@Param("taCodes") List<String> taCodes, @Param("pmtDt") String pmtDt);

    /**
     * 查询认缴金额信息
     *
     * @param fundCodes
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.SubsAmtInfoVo>
     * @author: huaqiang.liu
     * @date: 2020/9/30 15:51
     * @since JDK 1.8
     */
    List<SubsAmtInfoVo> queryCustSubsAmtInfo(@Param("fundCodes") List<String> fundCodes);

    /**
     * 修改认申购单上的认缴金额
     *
     * @param txAcctNo
     * @param fundCode
     * @param subsAmt
     * @return void
     * @author: huaqiang.liu
     * @date: 2020/10/13 16:02
     * @since JDK 1.8
     */
    void updateSubsAmt(@Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode, @Param("subsAmt") BigDecimal subsAmt);


    void updateAllSubsAmt(@Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode, @Param("subsAmt") BigDecimal subsAmt);
    List<HighDealOrderDtlPo> selectCustRedeemOrderDtl(HighDealOrderDtlPo params);


    /**
     * 统计订单表上报单数量
     *
     * @param mainDealNo
     * @return int
     * @author: huaqiang.liu
     * @date: 2021/4/9 10:32
     * @since JDK 1.8
     */
    int countDealOrderMergeSubmitNum(@Param("mainDealNo") String mainDealNo);

    /**
     * 根据主订单号查询订单列表
     *
     * @param mainDealNo
     * @return java.util.List<com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo>
     * @author: huaqiang.liu
     * @date: 2021/4/9 13:24
     * @since JDK 1.8
     */
    List<HighDealOrderDtlPo> selectOrdersByMainDealNo(@Param("mainDealNo") String mainDealNo);

    /**
     * 更新认申购订单的参与日期
     *
     * @param vo
     * @return int
     * @author: huaqiang.liu
     * @date: 2021/4/9 17:10
     * @since JDK 1.8
     */
    int updateBuyOrderJoinDt(SelectRedeemSplitSubCustBookVo vo);

    /**
     * 查询单条订单明细（机构对账）
     *
     * @param externalDealNo
     * @param dealNo
     * @param txAcctNo
     * @param disCode
     * @return com.howbuy.tms.high.batch.dao.vo.OtcHighDealOrderDtlVo
     * @author: huaqiang.liu
     * @date: 2021/4/26 16:03
     * @since JDK 1.8
     */
    OtcHighDealOrderDtlVo selectHighDealOrderDtlForOtc(@Param("externalDealNo") String externalDealNo, @Param("dealNo") String dealNo,
                                                       @Param("txAcctNo") String txAcctNo, @Param("disCode") String disCode);

    /**
     * 查询全部订单明细（机构对账）
     *
     * @param taTradeDt
     * @param disCode
     * @param taCode
     * @param mBusiCode
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.OtcHighDealOrderDtlVo>
     * @author: huaqiang.liu
     * @date: 2021/4/26 16:03
     * @since JDK 1.8
     */
    List<OtcHighDealOrderDtlVo> selectHighDealOrderDtlForOtcAll(@Param("taTradeDt") String taTradeDt, @Param("disCode") String disCode,
                                                                @Param("taCode") String taCode, @Param("mBusiCode") String mBusiCode);

    /**
     * 统计未支付成功的子订单数量
     *
     * @param mainDealNo
     * @return int
     * @author: huaqiang.liu
     * @date: 2021/6/8 10:21
     * @since JDK 1.8
     */
    int countNotPaySuccessSubOrders(@Param("mainDealNo") String mainDealNo);

    /**
     * @param tradeDtStart
     * @param tradeDtEnd
     * @param productIds
     * @return com.github.pagehelper.Page<com.howbuy.tms.high.batch.dao.vo.HighFundArrivalProofRecordVo>
     * @description:(查询需要生成资金到账证明文件的订单)
     * @author: haiguang.chen
     * @date: 2022/4/27 14:29
     * @since JDK 1.8
     */
    Page<HighFundArrivalProofRecordVo> selectLimitedCooperativeProductOrders(@Param("tradeDtStart") String tradeDtStart, @Param("tradeDtEnd") String tradeDtEnd, @Param("productIds") List<String> productIds);

    /**
     * @param txAcctNo
     * @param fundCode
     * @return java.math.BigDecimal
     * @description:(获取分次call总金额)
     * @author: haiguang.chen
     * @date: 2022/4/28 14:47
     * @since JDK 1.8
     */
    BigDecimal selectPeDivideCallAmt(@Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode,@Param("taTradeDt") String taTradeDt);

    /**
     * @param fundCode
     * @param submitTaDateBegin
     * @param submitTaDateEnd
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.AppointmentReportVo>
     * @description:(查询非预约的购买数据)
     * @author: haiguang.chen
     * @date: 2022/5/5 9:23
     * @since JDK 1.8
     */
    List<AppointmentReportVo> countPurchaseForNotAppoint(@Param("fundCode") String fundCode, @Param("submitTaDateBegin") String submitTaDateBegin, @Param("submitTaDateEnd") String submitTaDateEnd);

    /**
     * @param fundCode
     * @param submitTaDateBegin
     * @param submitTaDateEnd
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.AppointmentDetailVo>
     * @description:(查询非预约的购买数据Dtl)
     * @author:
     * @date:
     * @since JDK 1.8
     */
    List<AppointmentDetailVo> countPurchaseForNotAppointDtl(@Param("fundCode") String fundCode, @Param("submitTaDateBegin") String submitTaDateBegin, @Param("submitTaDateEnd") String submitTaDateEnd);

    /**
     * @param fundCode
     * @param submitTaDateBegin
     * @param submitTaDateEnd
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.AppointmentReportVo>
     * @description:(查询非预约的赎回数据)
     * @author: haiguang.chen
     * @date: 2022/5/5 9:23
     * @since JDK 1.8
     */
    List<AppointmentReportVo> countRedeemForNotAppoint(@Param("fundCode") String fundCode, @Param("submitTaDateBegin") String submitTaDateBegin, @Param("submitTaDateEnd") String submitTaDateEnd);

    /**
     * @param fundCode
     * @param submitTaDateBegin
     * @param submitTaDateEnd
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.AppointmentDetailVo>
     * @description:(查询非预约的赎回数据Dtl)
     * @author:
     * @date:
     * @since JDK 1.8
     */
    List<AppointmentDetailVo> countRedeemForNotAppointDtl(@Param("fundCode") String fundCode, @Param("submitTaDateBegin") String submitTaDateBegin, @Param("submitTaDateEnd") String submitTaDateEnd);

    /**
     * @param submitTaDate
     * @return java.util.List<com.howbuy.tms.high.batch.dao.vo.HighTradeReportCheckVo>
     * @description:(查询未回访数据)
     * @author: haiguang.chen
     * @date: 2022/5/9 13:42
     * @since JDK 1.8
     */
    List<HighTradeReportCheckVo> getNotCallBackForReport(@Param("submitTaDate") String submitTaDate);

    /**
     * @param dealDtlNo
     * @return com.howbuy.tms.high.batch.dao.vo.HighAppointInfoVo
     * @description:(查询订单信息)
     * @author: haiguang.chen
     * @date: 2022/6/29 17:17
     * @since JDK 1.8
     */
    HighAppointInfoVo getAppointInfoByDealDtlNo(@Param("dealDtlNo") String dealDtlNo);

    int updatePayOutStatusAndDtByOSerialNo(@Param("ackDt") String ackDt,
                                           @Param("refundDt") String refundDt,
                                           @Param("payDt") String payDt,
                                           @Param("orderPayStatus") String orderPayStatus,
                                           @Param("mBusiCodeList") List<String> mBusiCodeList,
                                           @Param("txAppFlagList") List<String> txAppFlagList,
                                           @Param("dealDtlNo") String dealDtlNo,
                                           @Param("orgSerialNo") String orgSerialNo);


    /**
     * 根据确认日期查询
     *
     * @param ackDt
     * @return
     */
    List<HighDealOrderDtlPo> selectByAckDt(@Param("ackDt") String ackDt);

    /**
     * 在途交易客户
     *
     * @param fundCodes
     * @return
     */
    List<HighDealOrderDtlPo> selectOnWays(@Param("fundCodes") List<String> fundCodes);

    /**
     * 获取订单明细及私募对账订单
     *
     * @param dealDtlNos
     * @return
     */
    List<HighDealDtlAndCheckOrderPo> selectHighDealDtlAndCheckOrder(@Param("dealDtlNos") List<String> dealDtlNos, @Param("ackDt") String ackDt,
                                                                    @Param("continuanceFlag") String continuanceFlag, @Param("taCode") String taCode);

    int updatePayOutStatusAndDtForSplit(@Param("payDt") String payDt,
                                        @Param("orderPayStatus") String orderPayStatus,
                                        @Param("orgSerialNo") String orgSerialNo);

    HighOnWayAmtInfoVo queryOnWayAmtInfo(@Param("txAcctNo") String txAcctNo, @Param("productCode") String productCode);

    HighOnWayAmtInfoVo queryAllOnWayAmtInfo(@Param("txAcctNo") String txAcctNo, @Param("productCode") String productCode);

    /**
     * 更新拆单标识
     *
     * @param fundCodes
     * @return
     */
    int updateStageFlag(@Param("fundCodes") List<String> fundCodes, @Param("ackDt") String ackDt);

    List<HighDealDtlAndCheckOrderPo> getAllRedeemSplitOrder(@Param("tradeDt") String tradeDt, @Param("taCode") String taCode);

    SpecialProductOrderPo getSpecialProductOrder(@Param("dealNo") String dealNo);

    Page<HighDealOrderDtlPo> queryOwnershipRightTransferOrder(QueryOwnershipRightTransferVo queryOwnershipRightTransferVo);


    List<HighDealOrderDtlPo> selectRedeemDealOrder(@Param("tradeDt")String tradeDt);

    void updateCxgDealNoByOriginSerialNo(@Param("cxgOrderNo")String cxgOrderNo,@Param("busiDealNo") String busiDealNo);

    void updateCxgDealNoByDealNo(@Param("dealNo")String dealNo,@Param("cxgOrderNo") String cxgOrderNo);

    List<MergeSubmitOrderVo> selectMergeSubmitOrderList(@Param("mainDealNos") List<String> mainDealNos);

    List<AppACKFlagVo> selectAppACKFlag(@Param("dealNo")String dealNo);

    List<HighDealReportVo> queryDealList(@Param("submitTaDt")String submitTaDt, @Param("fundCode")String fundCode,@Param("mBusiCode") String mBusiCode);

    /**
     * 根据taCode查询所有不重复的产品代码
     *
     * @param taCode TA代码
     * @return 产品代码列表
     * <AUTHOR>
     * @date 2025/01/15 10:30
     * @since JDK 1.8
     */
    List<String> selectDistinctFundCodesByTaCode(@Param("taCode") String taCode);
}