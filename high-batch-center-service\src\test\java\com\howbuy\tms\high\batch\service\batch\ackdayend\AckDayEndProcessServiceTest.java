/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.batch.ackdayend;

import com.howbuy.tms.high.batch.dao.po.batch.FundFileProcessDtlRecPo;
import com.howbuy.tms.high.batch.service.base.BaseTestSuite;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.repository.FundFileProcessDtlRecRepository;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.service.batch.ackdayend.AckDayEndProcessService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * @description: 确认日终处理服务测试类
 * <AUTHOR>
 * @date 2025/01/15 10:30
 * @since JDK 1.8
 */
public class AckDayEndProcessServiceTest extends BaseTestSuite {

    @Autowired
    private AckDayEndProcessService ackDayEndProcessService;

    @Mock
    private HighDealOrderDtlRepository mockHighDealOrderDtlRepository;

    @Mock
    private FundFileProcessDtlRecRepository mockFundFileProcessDtlRecRepository;

    @InjectMocks
    private AckDayEndProcessService mockAckDayEndProcessService;

    /**
     * 测试05文件检查 - 所有产品都有05文件的情况
     */
    @Test
    public void testCheck05FileExists_AllFilesExist() throws Exception {
        logger.info("testCheck05FileExists_AllFilesExist start");
        
        MockitoAnnotations.initMocks(this);
        
        String taTradeDt = "20250115";
        String taCode = "TA001";
        List<String> fundCodes = Arrays.asList("FUND001", "FUND002", "FUND003");
        
        // Mock 查询产品代码
        when(mockHighDealOrderDtlRepository.selectDistinctFundCodesByTaCode(taCode))
                .thenReturn(fundCodes);

        // Mock 所有产品都有05文件处理记录
        FundFileProcessDtlRecPo mockFileRecord = new FundFileProcessDtlRecPo();
        mockFileRecord.setRecordNo("TEST001");
        mockFileRecord.setFileOpStatus("3"); // 生成成功

        when(mockFundFileProcessDtlRecRepository.selectFundFileProcessDtlRec(eq(taTradeDt), eq("H_05_FILE"), eq(taCode), eq("FUND001")))
                .thenReturn(mockFileRecord);
        when(mockFundFileProcessDtlRecRepository.selectFundFileProcessDtlRec(eq(taTradeDt), eq("H_05_FILE"), eq(taCode), eq("FUND002")))
                .thenReturn(mockFileRecord);
        when(mockFundFileProcessDtlRecRepository.selectFundFileProcessDtlRec(eq(taTradeDt), eq("H_05_FILE"), eq(taCode), eq("FUND003")))
                .thenReturn(mockFileRecord);
        
        // 使用反射调用私有方法
        Method method = AckDayEndProcessService.class.getDeclaredMethod("check05FileExists", String.class, String.class);
        method.setAccessible(true);
        
        // 应该不抛出异常
        try {
            method.invoke(mockAckDayEndProcessService, taTradeDt, taCode);
            logger.info("testCheck05FileExists_AllFilesExist 测试通过 - 所有产品都有05文件");
        } catch (Exception e) {
            fail("不应该抛出异常: " + e.getMessage());
        }
        
        // 验证方法调用
        verify(mockHighDealOrderDtlRepository, times(1)).selectDistinctFundCodesByTaCode(taCode);
        verify(mockFundFileProcessDtlRecRepository, times(3)).selectFundFileProcessDtlRec(eq(taTradeDt), eq("H_05_FILE"), eq(taCode), anyString());
        
        logger.info("testCheck05FileExists_AllFilesExist end");
    }

    /**
     * 测试05文件检查 - 部分产品缺少05文件的情况
     */
    @Test
    public void testCheck05FileExists_SomeFilesMissing() throws Exception {
        logger.info("testCheck05FileExists_SomeFilesMissing start");
        
        MockitoAnnotations.initMocks(this);
        
        String taTradeDt = "20250115";
        String taCode = "TA001";
        List<String> fundCodes = Arrays.asList("FUND001", "FUND002", "FUND003");
        
        // Mock 查询产品代码
        when(mockHighDealOrderDtlRepository.selectDistinctFundCodesByTaCode(taCode))
                .thenReturn(fundCodes);

        // Mock 部分产品缺少05文件处理记录
        FundFileProcessDtlRecPo existingFileRecord = new FundFileProcessDtlRecPo();
        existingFileRecord.setRecordNo("TEST001");
        existingFileRecord.setFileOpStatus("3"); // 生成成功

        when(mockFundFileProcessDtlRecRepository.selectFundFileProcessDtlRec(eq(taTradeDt), eq("H_05_FILE"), eq(taCode), eq("FUND001")))
                .thenReturn(existingFileRecord);  // 有记录
        when(mockFundFileProcessDtlRecRepository.selectFundFileProcessDtlRec(eq(taTradeDt), eq("H_05_FILE"), eq(taCode), eq("FUND002")))
                .thenReturn(null);  // 缺少记录
        when(mockFundFileProcessDtlRecRepository.selectFundFileProcessDtlRec(eq(taTradeDt), eq("H_05_FILE"), eq(taCode), eq("FUND003")))
                .thenReturn(null);  // 缺少记录
        
        // 使用反射调用私有方法
        Method method = AckDayEndProcessService.class.getDeclaredMethod("check05FileExists", String.class, String.class);
        method.setAccessible(true);
        
        // 应该抛出异常
        try {
            method.invoke(mockAckDayEndProcessService, taTradeDt, taCode);
            fail("应该抛出BatchException异常");
        } catch (Exception e) {
            // 检查是否是预期的异常
            assertTrue("应该是BatchException", e.getCause() instanceof BatchException);
            BatchException batchException = (BatchException) e.getCause();
            String errorMessage = batchException.getMessage();
            
            // 验证错误信息格式
            assertTrue("错误信息应包含缺少的产品代码", errorMessage.contains("FUND002"));
            assertTrue("错误信息应包含缺少的产品代码", errorMessage.contains("FUND003"));
            assertTrue("错误信息应包含'缺少好买05明细文件'", errorMessage.contains("缺少好买05明细文件"));
            
            logger.info("testCheck05FileExists_SomeFilesMissing 测试通过 - 正确抛出异常: {}", errorMessage);
        }
        
        logger.info("testCheck05FileExists_SomeFilesMissing end");
    }

    /**
     * 测试05文件检查 - 该TA下没有产品的情况
     */
    @Test
    public void testCheck05FileExists_NoProducts() throws Exception {
        logger.info("testCheck05FileExists_NoProducts start");
        
        MockitoAnnotations.initMocks(this);
        
        String taTradeDt = "20250115";
        String taCode = "TA001";
        
        // Mock 查询产品代码返回空列表
        when(mockHighDealOrderDtlRepository.selectDistinctFundCodesByTaCode(taCode))
                .thenReturn(Collections.emptyList());
        
        // 使用反射调用私有方法
        Method method = AckDayEndProcessService.class.getDeclaredMethod("check05FileExists", String.class, String.class);
        method.setAccessible(true);
        
        // 应该不抛出异常
        try {
            method.invoke(mockAckDayEndProcessService, taTradeDt, taCode);
            logger.info("testCheck05FileExists_NoProducts 测试通过 - 没有产品时不抛出异常");
        } catch (Exception e) {
            fail("不应该抛出异常: " + e.getMessage());
        }
        
        // 验证方法调用
        verify(mockHighDealOrderDtlRepository, times(1)).selectDistinctFundCodesByTaCode(taCode);
        verify(mockFundFileProcessDtlRecRepository, never()).selectFundFileProcessDtlRec(anyString(), anyString(), anyString(), anyString());
        
        logger.info("testCheck05FileExists_NoProducts end");
    }

    /**
     * 测试HighDealOrderDtlRepository的selectDistinctFundCodesByTaCode方法
     */
    @Test
    public void testSelectDistinctFundCodesByTaCode() {
        logger.info("testSelectDistinctFundCodesByTaCode start");
        
        String taCode = "TA001";
        
        try {
            List<String> fundCodes = ackDayEndProcessService.getHighDealOrderDtlRepository().selectDistinctFundCodesByTaCode(taCode);
            logger.info("testSelectDistinctFundCodesByTaCode 查询结果: taCode={}, fundCodes={}", taCode, fundCodes);
            
            // 验证返回结果不为null
            assertNotNull("查询结果不应为null", fundCodes);
            
        } catch (Exception e) {
            logger.error("testSelectDistinctFundCodesByTaCode 测试失败", e);
            fail("查询产品代码失败: " + e.getMessage());
        }
        
        logger.info("testSelectDistinctFundCodesByTaCode end");
    }

    /**
     * 测试FundFileProcessDtlRecRepository的selectFundFileProcessDtlRec方法
     */
    @Test
    public void testSelectFundFileProcessDtlRec() {
        logger.info("testSelectFundFileProcessDtlRec start");

        String fundCode = "FUND001";
        String taTradeDt = "20250115";
        String taCode = "TA001";
        String fileType = "H_05_FILE";

        try {
            FundFileProcessDtlRecPo record = ackDayEndProcessService.getFundFileProcessDtlRecRepository()
                    .selectFundFileProcessDtlRec(taTradeDt, fileType, taCode, fundCode);
            logger.info("testSelectFundFileProcessDtlRec 查询结果: fundCode={}, taTradeDt={}, record={}",
                    fundCode, taTradeDt, record != null ? record.getRecordNo() : "null");

            // 这里只是验证方法能正常调用，不验证具体结果
            logger.info("testSelectFundFileProcessDtlRec 方法调用成功");

        } catch (Exception e) {
            logger.error("testSelectFundFileProcessDtlRec 测试失败", e);
            fail("查询05文件处理记录失败: " + e.getMessage());
        }

        logger.info("testSelectFundFileProcessDtlRec end");
    }
}
